// 网络请求返回信息
import facade from "../gameCore/Facade";
import { ConnectEvent } from "../login/define/LoginEnums";
import { ReceiveMsgMap, SendMsgMap } from "../../proto/msg-map";

type ResponseInfo = {
    err: string
    data?: any
}

type ReqCallbackData = {
    route: string
    cb: Function
    wait: boolean
    msg: any,
}

type routeObj = {
    route: string
    callback?: Function,
    ctx?: any
}

type ValueOf<T extends any, K extends keyof T> = T[K]

export default class NetworkModel {

    private client: any = null //Socket
    private reqId: number = 0 //请求id
    private reqMap: Map<number, ReqCallbackData> = new Map<number, ReqCallbackData>() //请求列表
    private events: routeObj[] = [] //当前事件列表
    private kick: boolean = false //是否被踢

    public clean() {
        this.offAll()
        this.reset()

    }

    public setKick(val: boolean = true) {
        this.kick = val
        this.offAll()
    }

    public reset() {
        this.kick = false
        this.reqId = 0
        this.reqMap.clear()
    }

    // 连接网络
    public async connect(prop: { host: string, port: number }) {
        if (this.isConnected()) {
            return
        }
        console.info('mqant connect:', prop.host + ':' + prop.port)
        return new Promise<boolean>(resolve => {
            this.close()
            const clientId = 'mqttjs_' + Math.random().toString(16).substring(2, 10)
            this.client = new Paho.MQTT.Client(prop.host, prop.port, '/mqtt', clientId)
            this.client.connect({
                onSuccess: (evt: any) => { //连接成功
                    this.reset()
                    // 设置客户端版本
                    this.gateReq(`ver=${go.version}`).then(() => {
                        console.info('connect success!', go.version)
                        facade.sendNt(ConnectEvent.ON_CONNECT_CREATE, 1);
                        resolve(true)
                    })
                },
                onFailure: (evt: any) => { //连接错误
                    facade.sendNt(ConnectEvent.ON_CONNECT_LOST, evt);
                    resolve(false)
                },
                mqttVersion: 3,
                useSSL: false,
                cleanSession: true,
                keepAliveInterval: 30, //心跳
            })
            // 注册连接断开处理事件
            this.client.onConnectionLost = (evt: any) => {
                if (evt.errorCode === 0 || evt.errorCode === 5) {
                    return
                }
                this.client = null
                facade.sendNt(ConnectEvent.ON_CONNECT_LOST, evt);
            }
            // 注册消息接收处理事件
            this.client.onMessageArrived = this.recvMessage.bind(this)
        })
    }

    // 关闭网络
    public close(emit: boolean = false) {
        if (this.isConnected()) {
            this.client.disconnect()
        }
        if (emit) {
            const fun = this.client.onConnectionLost
            fun({ errorCode: -1 })
        }
        this.client = null
    }

    public isConnected() {
        return !!this.client?.isConnected()
    }

    public async gateReq(arg: string) {
        if (!arg) return void console.error("gateReq：参数为空！！")
        return await this.sendReq(`gate/${arg}`)
    }

    /**
     *
     * @param route
     * @param msg
     * @param wait
     */
    private async sendReq(route: string, msg?: any, wait?: boolean): Promise<Uint8Array> {
        const msgName = route.replace('/', '_').toUpperCase()
        return new Promise<Uint8Array>(resolve => {
            this.reqId += 1
            this.reqMap.set(this.reqId, { cb: resolve, wait: !!wait, route, msg })
            route = route + '/' + this.reqId
            if (this.isConnected()) {
                console.log(`net req -> : `, route)
                let data = ""
                if (msg) {
                    data = msg.constructor.encode(msg).finish()
                }
                this.client.send(route, data, 1)
                return
            }
            facade.sendNt(ConnectEvent.ON_CONNECT_LOST, this.kick);
            console.log("login.net_error")
        })
    }

    /**
     * 发送net请求
     * @param route 路由
     * @param msg 消息对象
     */
    public async requestWithMsg<K extends keyof typeof SendMsgMap, V extends ValueOf<typeof SendMsgMap, K>, R extends ValueOf<typeof ReceiveMsgMap, K>>(route: K, msg: InstanceType<V>): Promise<InstanceType<R>> {
        const binary = await this.sendReq(route, msg)
        const el = ReceiveMsgMap[route]
        const replay = el.decode(binary) as InstanceType<R>
        // 处理error code
        if (replay && Object.prototype.hasOwnProperty.call(replay, 'code')) {
            const code = replay['code']
            if (code == 0) return replay
            go.onError && go.onError(code, route)
            return null
        }
        return replay
    }

    /**
     * 发送net请求
     * @param route 路由
     * @param args 组成消息的数据
     */
    public async request<K extends keyof typeof SendMsgMap, V extends ValueOf<typeof SendMsgMap, K>, R extends ValueOf<typeof ReceiveMsgMap, K>>(route: K, ...args: ConstructorParameters<V>): Promise<InstanceType<R>> {
        const msgType: V = SendMsgMap[route] as V
        // @ts-ignore
        const msg = msgType.create(...args) as InstanceType<V>
        const replay = await this.requestWithMsg(route, msg)
        return replay
    }

    private recvMessage(evt: any) {
        const [moduleType, func, msgid] = evt.destinationName.split('/')
        console.log(`net recv <- : `, evt.destinationName)
        if (msgid) {
            this.recvMessageById(msgid, evt.payloadBytes)
        } else if (moduleType) {
            // try一次转换
            const msgType = ReceiveMsgMap[moduleType]
            if (msgType) {
                let emitData = null
                try {
                    emitData = msgType.decode(evt.payloadBytes);
                } catch (e) {
                    emitData = null
                }
                if (!emitData) {
                    emitData = evt.payloadBytes
                }
                facade.sendNt(moduleType, emitData)
            } else {
                facade.sendNt(moduleType, evt.payloadBytes)
            }
        } else {
            console.error('recvMessage error msg:', evt.destinationName)
        }
    }

    private recvMessageById(msgid, payloadBytes) {
        const id = parseInt(msgid)
        const req = this.reqMap.get(id)
        if (req) {
            this.reqMap.delete(id)
            req.cb && req.cb(payloadBytes)
        }
    }

    // 监听
    public on(route: string, callback: Function, ctx?: any) {
        facade.offNt(route, callback, ctx)
        facade.onNt(route, callback, ctx)
        this.events.push({ route, callback, ctx })
    }

    // 注销监听
    public off(route: string, callback?: Function, ctx?: any) {
        facade.offNt(route, callback, ctx)
        this.events.delete(m => m.route === route && m.callback.toString() == callback.toString() && m.ctx == ctx)
    }

    public offAll() {
        this.events.forEach(m => facade.offNt(m.route, m.callback, m.ctx))
        this.events.length = 0
    }

    public isKick() {
        return this.kick
    }
}
