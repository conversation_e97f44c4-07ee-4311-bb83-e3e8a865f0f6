
import { delayCall } from '../delay'
import GameWorld from '../GameWorld'
import PlayerBag from '../bag/data/PlayerBag'

import GameSprite from './gs/GameSprite'
import { SprConst } from './gs/SprConst'
import { FilterUtil } from './FilterUtil'
import UIHandler from '../UIHandler'
import LevelOpen from '../LevelOpen'
import MoneyData from './MoneyData'
import { Define, MODEL_TYPE, MapType } from './Define'
import Item from '../vo/Item'
import Mission, { MissionConst } from '../mission/data/Mission'
import { MainPlayerAi } from '../MainPlayerAi'
import ModelVo from '../vo/ModelVo'
import { Tool } from './Tool'
import { ItemIconBase } from '../otherCmpt/common/ItemIconBase'
import MyPetVo from '../vo/MyPetVo'
import MsgHandler from '../MsgHandler'
import { LoadPri } from '../ExtWindow'
import Utilities from './Utilities'
import { DataDefine } from './DataDefine'
import PlayerVo from '../vo/PlayerVo'
import BuddyVo from '../buddy/data/BuddyVo'
import { CurrencyComView } from '../otherCmpt/common/CurrencyComView'

export class GUtil {
    public static createArray(e, t = 0, i?, o?) {
        for (var n, a = [], r = t; e > r; r++) {
            o || (n = null != i ? i : r)
            a.push(n)
        }
        return a
    }

    public static remove(e, t: boolean = false) {
        if (!e) return null
        e.parent && e.parent.removeChild(e)
        t && (Pool.release(e), (e = null))
        return e
    }

    public static copyList(e, t) {
        if (!e) return null
        for (var i, o = [], n = 0; n < e.length; n++) {
            i = e[n]
            o.push(i)
            t && o.push(i)
        }
        return o
    }

    public static center(e, t?) {
        if ((t || (t = e.parent), !t)) return e.x
        var i = (t.width - e.width) >> 1
        return i
    }

    /**
     * 向下取整
     * @param num 
     * @returns 
     */
    public static intNum(num: number): number {
        return 0 | num
    }

    public static initMoney(nodes: CurrencyComView[]) {
        for (let i = 0; i < nodes.length; i++) {
            const node = nodes[i]
            node.isNot = true
            node.textNum.text = '0'
        }
    }

    public static getMoney(it: CurrencyComView) { return parseInt(it.textNum.text) || 0 }

    public static setMoney(it: CurrencyComView, val: number = 0) { it.textNum.text = '' + val }

    public static setMoneys(e, t) {
        for (var i, o = 0; o < e.length; o++) {
            i = e[o]
            i.textNum.text = '' + t[o]
        }
    }

    public static getItemList(e, t = 6, i = false) {
        var o = e.length
        i && 0 >= o && (o = 1)
        for (var n, a = Math.ceil(o / t), r = a * t, s = [], l = 0; r > l; l++) {
            n = o > l ? e[l] : null
            s.push(n)
        }
        return s
    }

    public static getMinList<T extends any>(inputAry: T[], minNum: number, i: boolean = false) {
        if (!inputAry) return []
        const len = inputAry.length
        if (len >= minNum) return inputAry
        const output: T[] = []
        for (let a = 0; minNum > a; a++) {
            if (len > a) {
                output.push(inputAry[a])
            } else {
                i ? output.unshift(null) : output.push(null)
            }
        }
        return output
    }

    public static getMaxList<T extends any>(inputAry: T[], minNum: number) {
        let len = inputAry.length
        const output: T[] = []
        for (let i = 0; minNum > i; i++) {
            let item = len > i ? inputAry[i] : null
            output.push(item)
        }
        return output
    }

    public static getListNUll(e, t) {
        var i = e.length
        if (i >= t) return e
        for (var o = [], n = 0; t > n; n++) i > n ? o.push(e[n]) : o.push(null)
        return o
    }

    public static getListNullBeautify(e, t, i) {
        void 0 === i && (i = 1)
        for (var o = [], n = Math.max(e.length, t), a = 0; n > a; a++) a < e.length ? o.push(e[a]) : o.push(null)
        n = i - (o.length % i)
        n == i && (n = 0)
        for (var a = 0; n > a; a++) o.push(null)
        return o
    }

    public static getListNumNUll(e, t) {
        for (var i = e.length, o = [], n = 0; t > n; n++) i > n ? o.push(e[n]) : o.push(null)
        return o
    }

    public static listChangeState(e, t, i, o, n, a = false) {
        for (var r = [], s = 0; s < e.length; s++)
            if (e[s].hasOwnProperty(t) && e[s][t] == i && e[s].hasOwnProperty(o)) {
                e[s][o] = n
                a && r.push(e.splice(s, 1)[0])
                break
            }
        return this.listSplicing(e, r)
    }

    public static getListRew(e, t, i) {
        for (var o = 0; o < e.length; o++) if (e[o].hasOwnProperty(t) && e[o][t] == i) return true
        return false
    }

    public static listChangeStateOnes(e, t, i) {
        for (var o = 0; o < e.length; o++) e[o].hasOwnProperty(t) && (e[o][t] = i)
        return e
    }

    public static getListMoney(e) {
        for (var t = new MoneyData(), i = e.length - 1; i >= 0; i--)
            e[i].itemId == Define.MONEY1
                ? ((t.money1 += e[i].itemCount), e.splice(i, 1))
                : e[i].itemId == Define.MONEY2
                    ? ((t.money2 += e[i].itemCount), e.splice(i, 1))
                    : e[i].itemId == Define.MONEY3
                        ? ((t.money3 += e[i].itemCount), e.splice(i, 1))
                        : e[i].itemId == Define.EXP && ((t.exp += e[i].itemCount), e.splice(i, 1))
        return t
    }

    public static listReciprocal(e, t, i) {
        for (var o = [], n = e.length - 1; n >= 0; n--) e[n].hasOwnProperty(t) && e[n][t] == i && o.push(e.splice(n, 1)[0])
        return this.listSplicing(e, o)
    }

    public static listStatsuSort(e, t, i, o) {
        for (var n = [], a = [], r = e.length - 1; r >= 0; r--)
            e[r].hasOwnProperty(t) && (e[r][t] == i ? n.unshift(e.splice(r, 1)[0]) : e[r][t] == o && a.unshift(e.splice(r, 1)[0]))
        this.listSplicing(e, n, true)
        return this.listSplicing(e, a)
    }

    public static listReciprocalTow(e, t, i) {
        if (t.length != i.length) return e
        for (var o = [], n = e.length - 1; n >= 0; n--) {
            for (var a = 0, r = 0; r < t.length; r++) e[n].hasOwnProperty(t[r]) && e[n][t[r]] == i[r] && a++
            a == t.length && o.push(e.splice(n, 1)[0])
        }
        return this.listSplicing(e, o)
    }

    public static listSplicing(e, t, i?) {
        if (i === undefined) i = false
        if (i) for (var o = t.length - 1; o >= 0; o--) e.unshift(t[o])
        else for (var o = 0; o < t.length; o++) e.push(t[o])
        return e
    }

    public static getOnlineNum(e) {
        for (var t, i = 0, o = e.length, n = 0; o > n; n++) {
            t = e[n]
            t.isOnline && i++
        }
        return i
    }

    public static createTwoArray(e, t) {
        for (var i = new Array(e), o = 0; o < i.length; o++) i[o] = new Array(t)
        return i
    }

    public static covertTwoList(e, t) {
        var i = [],
            o = e.length,
            n: any = []
        for (var a = 0; o > a; a++) {
            n.push([e[a], t[a]])
            if ((a + 1) % 2 == 0) {
                i.push(n)
                n = []
            }
        }
        return i
    }

    public static chainArr(e, t, i) {
        void 0 === t && (t = 0)
        for (var o, n, a, r, s, l, u = 0, _ = e.length; _ > u; u++) {
            o = e[u]
            0 != u &&
                ((n = e[u - 1]),
                    (a = this.getWidth(n)),
                    (s = 0),
                    (l = 0),
                    i && i[u - 1] && (s = i[u - 1]),
                    o.anchorOffsetX && ((r = this.getWidth(o)), (l += r * o.anchorOffsetX)),
                    (o.x = n.x + a + s + t + l))
        }
    }

    public static getWidth(e) {
        var t = e.width * e.scaleX
        return (t = e instanceof eui.Label ? e.textWidth : e.width)
    }

    public static getArray(e, t, i?) {
        ; -1 == t && (t = 0)
        return e && t < e.length ? e[t] : i
    }

    public static bgFull(e) {
        if (e && e.parent) {
            var t = e.parent.width,
                i = e.parent.height
            if (!go.isPc) {
                var o = Math.max(t / e.width, i / e.height)
                e.width *= o
                e.height *= o
            }
            e.x = (t - e.width * e.scaleX) / 2
            e.y = (i - e.height * e.scaleY) / 2
        }
    }

    public static joinArray(e, t) {
        if (t && e) for (var i = t.length, o = 0; i > o; o++) e.push(t[o])
        return e
    }

    public static verticalArray(e, t) {
        var i = []
        if (e)
            for (var o = e.length, n = t, a = Math.floor(o / t), r = 0; n > r; r++) {
                i.push(e[r])
                for (var s = 0; a - 1 > s; s++) i.push(e[r + s * n + t])
            }
        return i
    }

    public static pos(e, t, i) {
        i.x = e
        i.y = t
    }

    public static size(e, t, i) {
        i.width = e
        i.height = t
    }

    public static drawRect(e, t, i, o, n, a) {
        null != a && e.graphics.beginFill(a)
        e.graphics.drawRect(t, i, o, n)
    }

    public static dealList(e, t, i) {
        if (!e) return
        if (i) {
            var o = e.indexOf(t)
                ; -1 == o && e.push(t)
        } else {
            var o = e.indexOf(t)
                ; -1 != o && e.splice(o, 1)
        }
    }

    public static dealList2(e, t, i, o, n) {
        for (var a, r, s = e.length, l = 0; s > l; l++) {
            a = e[l]
            r = this.findList(i, a, t)
            r && this.dealList(o, r, n)
        }
    }

    public static findList(e, t, i) {
        if (e) for (var o = e.length, n = void 0, a = 0; o > a; a++) if (((n = e[a]), n[i] == t)) return n
    }

    public static compareEquip(item: Item, compareItem?: Item) {
        let yes: boolean = false
        if (!compareItem) {
            compareItem = GameWorld.myPlayer ? PlayerBag.getCompareEquip(GameWorld.myPlayer.bag, item.type) : null
        }
        if (compareItem == null) {
            yes = true
        } else {
            if (item.reqLv > compareItem.reqLv || item.grade > compareItem.grade) yes = true
        }
        return yes
    }

    public static isSuitableWeapon(e, t) {
        if ((void 0 === t && (t = GameWorld.myPlayer), !e.isEquipClass())) return false
        var i = false
        switch (t.job) {
            case Define.JOB_RANGER:
                ; (e.type == Define.ITEM_TYPE_WEAPON_ONEHAND_SWORD || e.type == Define.ITEM_TYPE_WEAPON_ONEHAND_BLADE) && (i = true)
                break
            case Define.JOB_XIUZHEN:
                e.type == Define.ITEM_TYPE_WEAPON_TWOHAND_STAFF && (i = true)
                break
            case Define.JOB_WARRIOR:
                ; (e.type == Define.ITEM_TYPE_WEAPON_TWOHAND_SWORD || e.type == Define.ITEM_TYPE_WEAPON_TWOHAND_BLADE) && (i = true)
                break
            case Define.JOB_WIZARD:
                e.type == Define.ITEM_TYPE_WEAPON_TWOHAND_STAFF && (i = true)
        }
        return i
    }

    public static compareEquipByJob(e, t?, i?) {
        var o = this.isJobEquipByBody(e, null, i)
        return o ? this.compareEquip(e, t) : void 0
    }

    public static isJobEquipByBody(e, t?, i?) {
        t || (t = GameWorld.myPlayer)
        var o = false
        Item.isValidEquipRequire(t, e, null) && (o = true)
        var a = PlayerBag.getCompareEquip(t.bag, e.type)
        i || (a && a.type != e.type && (o = false))
        return o
    }

    public static findNpc(t: {
        npcId?: number
        npcName?: string
        mapId?: number
        mapType?: MapType
        isGuide?: boolean
        npcId2?: number
        npcName2?: string
        px?: number
        py?: number
    }): boolean {
        let i: boolean | undefined
        let o: boolean | undefined
        let n = t.npcId
        const a = t.npcName
        const r = t.mapId
        const s = t.mapType
        const l = GameWorld.mapId
        const c = s === MapType.city

        if (n == null && a) n = GameWorld.getNpcIdByName(a)
        if (c && t.isGuide) o = true

        if (s) {
            if (c) {
                i = Define.isAllocateCityMap(l)
            }
        } else if (r) {
            i = r === l
        } else if (this.isNpcShow(n)) {
            i = true
        }

        if (i) {
            if (this.isNpcShow(n)) Mission.autoRunNpc(n)
            else if (o) {
                if (Define.isAllocateCityMap(l)) {
                    if (t.npcId2) n = t.npcId2
                    else if (t.npcName2) n = GameWorld.getNpcIdByName(t.npcName2)
                }
                if (this.isNpcShow(n)) Mission.autoRunNpc(n)
            }
        } else if (((MainPlayerAi.ins.autoNpcData = t), t.mapId && !c)) {
            const p = t.px || 10
            const h = t.py || 10
            GameWorld.doJumpMap(t.mapId, p, h)
        }

        return !!i
    }

    public static isNpcShow(e) {
        var t = GameWorld.getNpcByID(e)
        return t && t.isVisible() && t.isEnable() ? true : void 0
    }

    public static findItem(e, t) {
        var i = -1
        if (e && e.source)
            for (var o = e.source.length, n = void 0, a = void 0, r = 0; o > r; r++) {
                n = e.source[r]
                a = n ? n.item : n
                a && a.slotPos == t && (i = r)
            }
        return i
    }

    public static scrollItem(e: any, t: number, i?: boolean): number {
        let o = e.dataProvider
        if (i) t = this.findItem(o, t)
        if (t !== -1) {
            let n: number | undefined
            const a = e.parent ? e.parent.width : e.width
            const r = e.parent ? e.parent.height : e.height
            let s = 0,
                l = 0,
                u: boolean | undefined

            if (e.numChildren > 0) {
                const _ = e.getChildAt(0)
                s = _.width
                l = _.height
                let d = 0,
                    c = 0

                if (e.layout) {
                    if (e.layout instanceof eui.TileLayout) {
                        d = e.layout.horizontalGap
                        c = e.layout.verticalGap
                    } else if (e.layout instanceof eui.VerticalLayout) {
                        d = 0
                        c = e.layout.$gap
                    } else if (e.layout instanceof eui.HorizontalLayout) {
                        d = e.layout.$gap
                        c = 0
                        u = true
                    }
                }

                if (u) n = t * (s + d) - a
                else {
                    const p = Math.floor(a / (s + d))
                    const h = Math.ceil(t / p)
                    const f = Math.floor(h * (l + c))
                    n = f - r
                }
            }

            if (n > 0) u ? (e.scrollH = n) : (e.scrollV = n)
        }

        return t
    }

    public static scrollList(e: any, t: number, i?: any, o?: boolean) {
        egret.callLater(function () {
            if (!i) {
                let n = e.parent
                while (n) {
                    if (n instanceof eui.Scroller) {
                        i = n
                        break
                    }
                    n = n.parent
                }
            }

            const a = i ? i.width : e.width
            const r = i ? i.height : e.height
            const s = e.dataProvider

            if (s.length) {
                t = t > s.source.length ? s.source.length : t + 1

                let l: boolean | undefined,
                    u: number,
                    _ = 0,
                    d = 0,
                    c = 0,
                    p = 0

                if (e.layout) {
                    if (e.layout instanceof eui.TileLayout) {
                        _ = e.layout.horizontalGap
                        d = e.layout.verticalGap
                    } else if (e.layout instanceof eui.VerticalLayout) {
                        d = e.layout.$gap
                    } else if (e.layout instanceof eui.HorizontalLayout) {
                        _ = e.layout.$gap
                        l = true
                    }
                }

                i && i.viewport.validateNow()

                const h = e.getVirtualElementAt(0)
                c = h.width
                p = h.height

                if (l) u = t * (c + _) - a
                else {
                    const f = Math.floor(a / (c + _))
                    const T = Math.ceil(t / f)
                    const E = Math.floor(T * (p + d))
                    u = E - r
                }

                if (u > 0) {
                    if (!o) {
                        let y = 0
                        if (i) {
                            let g: number | undefined, v: number | undefined
                            if (l) {
                                y = a / 2 - c / 2
                                g = i.measuredWidth
                                v = a
                            } else {
                                y = r / 2 - p / 2
                                g = i.measuredHeight
                                v = r
                            }
                            if (g > u + y + v) {
                                u += y
                            }
                        }
                    }

                    if (i) l ? (i.viewport.scrollH = u) : (i.viewport.scrollV = u)
                    else l ? (e.scrollH = u) : (e.scrollV = u)
                }

                i && i.viewport.validateNow()
            }
        }, this)
    }

    public static scrollHV(e, t, i, o = 350) {
        delayCall(
            Handler.alloc(
                this,
                function (e, t, i) {
                    if (t && !(t.numChildren <= 0)) {
                        0 > i && (i = 0)
                        var o = false,
                            n = 0,
                            a = 0,
                            r = 0,
                            s = 0,
                            l = 0,
                            u = 0,
                            _ = 0,
                            d = t.dataProvider.source.length,
                            c = t.$children.length,
                            p = 1,
                            h = 0,
                            f = 0
                        t.layout instanceof eui.HorizontalLayout
                            ? ((n = t.layout.gap), (r = e.width), (a = 0), (s = e.height), (p = 1), (h -= t.layout.paddingRight), (h += t.layout.paddingLeft))
                            : t.layout instanceof eui.VerticalLayout
                                ? ((a = t.layout.gap), (s = e.height), (n = 0), (r = e.width), (p = 2), (f += t.layout.paddingTop), (f += t.layout.paddingBottom))
                                : t.layout instanceof eui.TileLayout &&
                                ((a = t.layout.verticalGap),
                                    (n = t.layout.horizontalGap),
                                    (s = e.height),
                                    (r = e.width),
                                    (p = 3),
                                    (h -= t.layout.paddingRight),
                                    (h += t.layout.paddingLeft),
                                    (f -= t.layout.paddingTop),
                                    (f += t.layout.paddingBottom))
                        i >= d && (i = d - 1)
                        for (var T = 0, E = 0, y = 0, g = true, v = 0; d > v; v++) {
                            try {
                                if (c > v) {
                                    var S = t.getChildAt(v)
                                    T = S.height
                                    E = S.width
                                }
                            } catch (I) {
                            }
                            if (
                                (g && ((g = false), (y = Math.floor(r / (E + n)) * (E + n))),
                                    1 == p && (l += E + n),
                                    (2 == p || 3 == p) && (_ += E + n) >= y && ((_ = 0), (u += T + a)),
                                    v == i)
                            ) {
                                l = l >= r ? l - r - n + h : 0
                                u = u >= s ? u - s - a + f : 0
                                o = true
                                break
                            }
                        }
                        o && (2 == p || 3 == p ? (e.viewport.scrollV = u) : (e.viewport.scrollH = l))
                    }
                },
                [e, t, i],
                true
            ),
            o
        )
    }

    public static dealPoint(e: any, t?: string[]) {
        t || (t = ['x', 'y', 'width', 'height'])
        for (let i = 0; i < t.length; i++) {
            var key = t[i]
            e[key] = Math.floor(e[key])
        }
    }

    public static getMoneyItem(asItem: boolean, itemId: number, num: number) {
        let data: Item | proto.ItemData
        if (asItem) {
            data = new Item()
            data.quantity = num
            data.id = itemId
        } else {
            data = new proto.ItemData()
            data.quantity = num
            data.id = itemId
        }

        //data.name = ModelVo.getMoneyText(itemId)
        //data.bagIcon = Tool.getMoneyIcon(itemId)
        return data
    }

    public static itemInfoClone(e) {
        var t = new pb.itemInfo()
        t.name = e.name
        t.bagIcon = e.bagIcon
        t.effectId = e.effectId
        t.fashIcons = e.fashIcons
        t.grade = e.grade
        t.hasAcquired = e.hasAcquired
        t.hasLock = e.hasLock
        t.hasSelect = e.hasSelect
        t.haveNum = 0 == e.haveNum ? null : e.haveNum
        t.icon = e.icon
        t.info = e.info
        t.itemCount = e.itemCount
        t.itemId = e.itemId
        t.hasHelp = e.hasHelp
        return t
    }

    public static getTaskStatu(e, t?) {
        t = t || GameWorld.myPlayer
        var i = MissionConst.CAN_ACCEPT
        if (Mission.isMissionFinish(t, e)) i = MissionConst.FINISH
        else {
            var o = t.getMissionById(e)
            o && (i = o.getMissionStatus(t))
        }
        return i
    }

    public static isTouchItem(e, t?) {
        var i,
            o = e.target
        if (o instanceof ItemIconBase) i = o
        else {
            var n = o.parent
            n && n instanceof ItemIconBase && (i = n)
        }
        return i ? (t || i.showTip(), true) : void 0
    }

    public static getLeader() {
        var e = GameWorld.getTypeModel(Define.NEAR_MY_TEAM)
        if (e)
            for (var t = 0; t < e.arr.length; t++) {
                var i = e.arr[t]
                if (i.isLeader) return i
            }
    }

    public static setLayout(e, t) {
        e.visible != t && ((e.visible = t), (e.includeInLayout = t))
    }

    public static checkHead(e) {
        var t = e.pet
        if (!t) return
        if (!e.isServerTeam) return void UIHandler.alertMessage('跨服成员不支持查看')
        var i = GameWorld.myPlayer
        return i.isServerTeam_2 ? void UIHandler.alertMessage('跨服活动中不支持查看') : void MyPetVo.doPetInfoMsg(t, MsgHandler.PET_INFO_SEE_PLAYER, [e.id])
    }

    public static listenTabList(e, t, i) {
        for (var o, n = e.tabList, a = 0; a < n.length; a++) {
            o = n[a]
            o.currentState = 'up'
            i || (e.listen(o, t.bind(this, a, o, e)), e.tabIndex == a && (o.currentState = 'labUp'))
        }
    }

    public static resetTabList(e) {
        this.listenTabList(e, null, true)
    }

    public static createTestList(e, t, i) {
        void 0 === t && (t = 20)
        var o,
            n,
            a = [],
            r = i ? e : new e()
        r.setDefault && r.setDefault()
        for (var s = 0; t > s; s++) {
            o = {}
            for (var l in r) {
                n = r[l]
                Object.prototype.hasOwnProperty.call(r, l) && ('number' == typeof n ? (n = s + 1) : 'string' == typeof n && (n = '测试名称' + (s + 1)))
                o[l] = n
            }
            a.push(o)
        }
        return a
    }

    public static updateVo(e, t) {
        var i
        for (var o in t || {}) t.hasOwnProperty(o) && ((i = t[o]), (e[o] = i))
        return e
    }

    public static showHideList(e, t: boolean = false) {
        for (var i, o = e.length, n = 0; o > n; n++) {
            i = e[n]
            i.visible = t
        }
    }

    public static coveryDataList(e, t, i) {
        var o = []
        if (e)
            for (var n = e.length, a = i.length, r = void 0, s = void 0, l = void 0, u = 0; n > u; u++) {
                r = e[u]
                s = new t()
                for (var _ = 0; a > _; _++) {
                    l = i[_]
                    s[l] = r[l]
                }
                o.push(s)
            }
        return o
    }

    public static isTxtOut(e, t) {
        t || (t = e.text)
        var i = Math.floor(e.width / e.size)
        return t.length > i
    }

    public static openMainInfo(e) {
        LevelOpen.ins.openMainInfoById(e)
    }

    public static openMainInfoArr(e) {
        for (var t = [], i = 0; i < e.length; i++) {
            var o = LevelOpen.ins.getGuideLevelData(e[i])
            o && !o._isUnlock && (t = t.concat(o.atlas))
        }
        UIHandler.openMainInfo(t)
    }

    public static getEqualPartList(e, t, i) {
        for (var o = [], n = Math.floor(e / t), a = i ? 1 : 0, r = a; t > r; r++) o.push(n * r)
        o.push(e)
        return o
    }

    public static setGray(e, t, i = true) {
        i && (e.touchEnabled = !t)
        e.filters = t ? FilterUtil.grayFilter : void 0
    }

    public static dealLockList(e, t, i, o?, n?) {
        if (e) {
            for (let r = 0; r < e.length; r++) {
                this.dealLock(e[r], t, i, o, n)
            }
        }
        return e
    }

    public static dealLock(e, hasLock, hasAcquired, o?: boolean, n?: SprConst) {
        e.hasLock = hasLock
        e.hasAcquired = hasAcquired
        o && (n || (n = SprConst.SPRITE_ITEM_ENCHANT))
        e.effectId = o ? n : 0
        return e
    }

    public static createSprite(vo: PlayerVo, isBattle: boolean) {
        const sprite = Pool.alloc(GameSprite)
        const n = vo.getType()
        switch (true) {
            case n == MODEL_TYPE.TYPE_PET || n == MODEL_TYPE.TYPE_PET_MONSTER:
                sprite.setPetIcon(vo.icon1, isBattle)
                break
            case n == MODEL_TYPE.TYPE_BUDDY:
                sprite.setSrc((vo as BuddyVo).icon, LoadPri.UIScene, isBattle)
                break
            case n == MODEL_TYPE.TYPE_LEGION || n == MODEL_TYPE.TYPE_PLAYER || n == MODEL_TYPE.TYPE_MERCENARY || n == MODEL_TYPE.TYPE_PLAYER_MONSTER:
                const change = vo.getIconpet1()
                if (change) {
                    sprite.setPetIcon(Long.fromNumber(change))
                } else {
                    sprite.setIcon(vo.icon1, vo.icon2, vo.icon3, isBattle)
                }
                break
            default:
                sprite.setSrc(vo.icon1, LoadPri.UIScene, isBattle)
        }
        sprite.scale = 2
        return sprite
    }

    public static isHasRemind(e) {
        var t = egret.localStorage.getItem(e + GameWorld.myPlayer.id),
            i = Utilities.getTimeDay()
        return t == i
    }

    public static setRemind(e, t = true) {
        var i = Utilities.getTimeDay(),
            o = e + GameWorld.myPlayer.id
        t ? egret.localStorage.setItem(o, i) : egret.localStorage.removeItem(o)
    }

    public static eqLong(e, t) {
        for (var i = 0; i < e.length; i++) if (t.eq(e[i])) return true
        return false
    }

    public static getAlertRemind(t) {
        return this.isHasRemind(DataDefine.alertRemind + '_' + t)
    }

    public static setAlertRemind(t, i) {
        void 0 === i && (i = true)
        this.setRemind(DataDefine.alertRemind + '_' + t, i)
    }
}
