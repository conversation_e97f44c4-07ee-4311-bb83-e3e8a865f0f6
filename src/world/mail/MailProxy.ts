import { ProxyBase } from '../gameCore/ProxyBase'
import ProtocolDefine from '../ProtocolDefine'
import { MailModel } from './data/MailModel'
import ProtoUtil from '../ProtoUtil'
import GameWorld from '../GameWorld'
import StringBuffer from '../gameCore/StringBuffer'

import <PERSON>g<PERSON>and<PERSON> from '../MsgHandler'
import MailTool from './MailTool'
import { Tool } from '../gameCore/Tool'
import GameText from '../gameCore/GameText'
import facade from '../gameCore/Facade'
import { MailEvent } from './define/MailEvent'
import MailDefine from './define/MailDefine'
import UIHandler from '../UIHandler'
import { Define } from '../gameCore/Define'
import TimeMgr from '../gameCore/TimeMgr'
import { Tips } from '../otherCmpt/common/Tips'
import { ObjectData } from '../ObjectData'
import { Message } from '../Message'
import { Proxy } from '../gameCore/Proxy'
import { Msg } from '../../proto/msg-define'

type MailNumInfo = {
    read: number
    unRead: number
}
export default class MailProxy extends ProxyBase {
    public deleteMails: any
    public mailNums: { [key: number]: MailNumInfo }
    public mailNum: number
    private _data: MailModel
    public isSelectDelete: boolean

    constructor() {
        super()
        this.deleteMails = {}
        this.mailNums = {}
        this.mailNum = 0
    }

    public get data() {
        return this._data
    }

    public initialize() {
        this._data = new MailModel()
        this.network.on(Msg.S2C_NewMailMessage, this.onNewMailMessage, this)

        this.onProto(ProtocolDefine.CG_MAIL_SEND_SERVICE, this.onGMMsg)
        this.onProto(ProtocolDefine.CG_MAIL_REPORT, this.onReport)
        // this.onProto(ProtocolDefine.CG_MAIL_NOTIFY, this.processMailNotifyMsg)
        this.onProto(ProtocolDefine.CG_MAIL_EXTRACT_ATTACHMENT_ALL, this.onExtractAll)
    }

    public update() { }

    public onExtractAll(e) {
        var t = ProtoUtil.dealResult(e)
        if (t) {
            var i = GameWorld.myPlayer
            if (i) {
                var o,
                    n = [],
                    r = new StringBuffer(),
                    d = new StringBuffer()
                i.setMoneyByType(proto.ModelConst.Type.MONEY1, t.getInt(), d)
                i.setMoneyByType(proto.ModelConst.Type.MONEY2, t.getInt(), d)
                i.setMoneyByType(proto.ModelConst.Type.MONEY3, t.getInt(), d)
                o = MsgHandler.processAddItemMsg(t, ProtocolDefine.ADD_ITEM_MAIL_REWARD, true)
                n = n.concat(o)
                r.append(t.getMsgInfo())
                for (var p, h = t.getByte(), f = t.readShort(), T = 0; f > T; T++) {
                    p = t.getLong()
                    this.dealMailData(p, h)
                }
                MailTool.dealReward(n, Tool.manageString(GameText.STR_MAIL_PICK_SUCCESS_ALL_INFO, r.toString()), GameText.getText(GameText.TI_WARM_SHOW))
                facade.sendNt(MailEvent.MAIL_STATU)
            }
        }
    }

    public dealMailData(e, t) {
        var i = this._data.getMailList(t)
        if (i && i.length)
            for (var o = i[0].arr, n = o.length, a = void 0, r = 0; n > r; r++)
                if (((a = o[r]), a.id.eq(e))) {
                    a.setTabStatus(true, MailDefine.DEL_STATUS)
                    break
                }
    }

    public onReport(e) {
        UIHandler.alertMessage(GameText.STR_MAIL_INFORM_SUCCESS)
        facade.sendNt(MailEvent.MAIL_CLOSE_RECEIVE)
    }

    public onGMMsg(e) {
        UIHandler.alertMessage(GameText.STR_MAIL_SEND_GM_SUCCESS)
    }

    // 打开邮件界面后 先请求邮件数量信息
    public async reqMailNumInfo() {
        const r = await this.network.request(Msg.C2S_OnMailOpenMessage)
        if (!r) return
        for (const info of r.simpleNumInfo) {
            this.mailNums[info.type] = {
                read: info.read,
                unRead: info.unread
            }
            // 这里显示的是未读邮件数量
            const index = this.data.getMailNumIndex(info.type)
            this.data.setMailNum(index, info.unread)
        }
        this.data.updateRp()
        facade.sendNt(MailEvent.MAIL_OPEN)

    }

    // 收到新邮件
    private onNewMailMessage() {
        const a = ObjectData.fromPlayerEventBytes(Date.now(), 0, Define.PLAYER_EVENT_MAIL, TimeMgr.timer.serverTime + 120000, GameText.getText(GameText.TI_MAIL), "", '', '')
        GameWorld.addPlayerEvent(a)
        facade.sendNt(MailEvent.MAIL_RP, true)
        facade.sendNt(MailEvent.MAIL_NEW)
    }

    public onEnter() {
        this.sendMailOpen()
    }

    public sendMailOpen() {
        console.log("sendMailOpen")
        // var e = MsgHandler.createMailNewNotice()
        // Proxy.Service.sendProto(e)
    }

    public sendExtractAll(e, t, i) {
        var n = new Message(ProtocolDefine.CG_MAIL_EXTRACT_ATTACHMENT_ALL)
        n.putByte(e)
        n.putByte(t)
        n.putShort(i)
        console.log('sendExtractAll', e, t, i)
        Proxy.Service.sendProto(n)
    }
}
