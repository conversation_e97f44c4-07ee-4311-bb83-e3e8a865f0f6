import Facade from '../../gameCore/Facade'
import MailListItemView from '../view/MailListItemView'
import MailListView from '../view/MailListView'
import MailProxy from '../MailProxy'
import MdrBase from '../../gameCore/MdrBase'
import { Layer } from '../../Layer'
import { ModName } from '../../viewType/ModName'
import { ProxyType } from '../../viewType/ProxyType'
import { MailEvent } from '../define/MailEvent'
import { Tips } from '../../otherCmpt/common/Tips'
import MailTool from '../MailTool'
import MailDefine from '../define/MailDefine'
import UIHandler from '../../UIHandler'
import { MailViewType } from '../../viewType/MailViewType'
import facade from '../../gameCore/Facade'
import { MailVo } from '../data/MailVo'


export default class MailListMdr extends MdrBase<MailListView, proto.MailDefine.MAIL_TYPE> {
    private _proxy: MailProxy
    private _data: eui.ArrayCollection
    private _cur: number
    private _max: number
    private _type: proto.MailDefine.MAIL_TYPE
    private _isCan: boolean

    constructor() {
        super(Layer.window)
        this.mark('_view', MailListView)
    }

    public get model() {
        return facade.getProxy(ModName.Mail, ProxyType.Mail).data
    }

    public onInit() {
        super.onInit()
        this._view.top = 0
        this._view.bottom = 0
        this._view.horizontalCenter = 0
        this._proxy = this.getProxy(ProxyType.Mail)
        this._data = new eui.ArrayCollection()
        this._view.list.itemRenderer = MailListItemView
        this._view.list.dataProvider = this._data
    }

    public addListeners() {
        this.listen(this._view.btnClose, this.tapBack)
        this.listen(this._view.btnAllGet, this.onAllGet)
        this.listen(this._view.btnSelect, this.mailSelect)
        this.listen(this._view.btnDelete, this.mailDelete)
        this.listen(this._view.btnPre, this.onChangePage.bind(this, -1))
        this.listen(this._view.btnNext, this.onChangePage.bind(this, 1))
        this.onNt(MailEvent.MAIL_STATU, this.updateList)
        this.onNt(MailEvent.MAIL_LIST, this.updateList)
    }

    public onChangePage(step: number) {
        const page = this._cur + step
        if (page <= 0) {
            return void Tips.show('翻页已到最前页，无法继续翻页')
        }
        else if (page > this._max) {
            return void Tips.show('翻页已到最末页，无法继续翻页')
        }
        this._cur = page
        MailTool.doMailListMsg(this._type, MailDefine.pageShowNum, this._cur)
    }

    public onAllGet() {
        if (this._isCan) {
            return void this._proxy.sendExtractAll(this._type, MailDefine.pageShowNum, this._cur)
        }
        this._proxy.data.openService()
    }

    public onShow() {
        let title = '邮件'
        this._type = MailDefine.MAIL_TYPE_PLAYER
        if (this._showArgs) {
            this._type = this._showArgs
            title = this.model.getWin(this._type, true)
        }
        this._cur = 1
        const canAttachAll = MailTool.isAllAttach(this._type)
        this._isCan = canAttachAll
        this._view.btnAllGet.label = canAttachAll ? '全部领取' : '联系客服'
        this._view.txtTitle.text = title
        this._proxy.isSelectDelete = false
        this._view.btnSelect.label = '批量删除'
        this._view.btnSelect.visible = this._view.btnDelete.visible = this._type == MailDefine.MAIL_TYPE_MONEY
        this.updateList()
    }

    public updateList() {
        const list = this.model.getMailList(this._type)
        if (!list.length) return
        const vec = list[0]
        this._data.source = vec.arr
        let newCnt = this.getNewNum(vec)
        let total = list[1]
        let addPage = total % MailDefine.pageShowNum == 0 ? 0 : 1
        this._view.txtNum.text = newCnt + '/' + total
        this._max = Math.floor(total / MailDefine.pageShowNum) + addPage
        this._view.txtPage.text = this._cur + '/' + this._max
    }

    public getNewNum(vec: Vector<MailVo>) {
        let total = 0
        for (let i = 0; i < vec.length; i++) {
            const t = vec.elementAt(i)
            if (t.getStatus() == MailDefine.MAIL_STATU_NEW) {
                total++
            }
        }
        return total
    }

    public tapBack() {
        UIHandler.hideView(ModName.Mail, MailViewType.MailList)
    }

    public mailSelect() {
        this._proxy.isSelectDelete = !this._proxy.isSelectDelete
        this._view.btnSelect.label = this._proxy.isSelectDelete ? '取消删除' : '批量删除'
        this.updateList()
    }

    public mailDelete() {
        const _this = this
        let t = Object.keys(this._proxy.deleteMails).length
        if (!this._proxy.isSelectDelete || 0 == t) return void Tips.show('请选择要删除的邮件！')
        for (const key in this._proxy.deleteMails) {
            const o = this._proxy.deleteMails[key]
            if (o.status <= MailDefine.MAIL_STATUS_UNREAD_TRADE || o.status == MailDefine.MAIL_STATUS_READ_ITEM) {
                return void UIHandler.alertMessage(
                    "要删除的邮件中包含有/cec7676未读邮件/p或/cec7676未领取的附件/p，确定要删除邮件?\n/cec7676注意: 邮件删除后所包含的附件将无法找回!/p",
                    null,
                    Handler.alloc(this, function () {
                        MailTool.doDeleteAllMailMsg(_this._type)
                    })
                )
            }
        }
        MailTool.doDeleteAllMailMsg(this._type)
    }
}
