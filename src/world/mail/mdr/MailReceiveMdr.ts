import MdrBase from "../../gameCore/MdrBase";
import MailReceiveView from "../view/MailReceiveView";
import { Layer } from "../../Layer";
;
import { MailEvent } from "../define/MailEvent";
import { ListBagItem } from "../../ListBagItem";
import UIHandler from "../../UIHandler";
import MailTool from "../MailTool";
import { MailVo } from "../data/MailVo";
import MailDefine from "../define/MailDefine";
import TextUtil from "../../gameCore/TextUtil";
import { GUtil } from "../../gameCore/GUtil";
import { ModName } from "../../viewType/ModName";
import { MailViewType } from "../../viewType/MailViewType";
import Item from "../../vo/Item";
import { CurrencyComView } from "../../otherCmpt/common/CurrencyComView";

export default class MailReceiveMdr extends MdrBase<MailReceiveView, MailVo | null> {

    public pos3: number;
    private _dataForBagList: eui.ArrayCollection;
    public mail: MailVo
    public item: Item
    public statu: number
    public statu2: number

    constructor() {
        super(Layer.window)
        this.mark("_view", MailReceiveView);
    }

    public onInit() {
        this._view.top = 0;
        this._view.bottom = 0;
        this._view.horizontalCenter = 0;
        this._view.moneyreceive1.setType(proto.ModelConst.Type.MONEY1);
        this._view.moneyreceive2.setType(proto.ModelConst.Type.MONEY2);
        this._view.moneyreceive3.setType(proto.ModelConst.Type.MONEY3);
        this._view.moneySend1.setType(proto.ModelConst.Type.MONEY1);
        this._view.moneySend2.setType(proto.ModelConst.Type.MONEY2);
        this._view.moneySend3.setType(proto.ModelConst.Type.MONEY3);
        this.pos3 = this._view.moneySend3.x;
        this._view.listBagItem.itemRenderer = ListBagItem;
        this._dataForBagList = new eui.ArrayCollection();
        this._view.listBagItem.dataProvider = this._dataForBagList;
    }

    public addListeners() {
        this.listen(this._view.btnClose, this.tapBack);
        this.listen(this._view.btnReply, this.onReply);
        this.listen(this._view.btnReject, this.onReject);
        this.listen(this._view.btnAttach, this.onAttach);
        this.listen(this._view.btnReport, this.onReport);
        this.listen(this._view.listBagItem, this.tapItem, eui.ItemTapEvent.ITEM_TAP);
        this.onNt(MailEvent.MAIL_CLOSE_RECEIVE, this.autoClose);
    }

    public autoClose() {
        this.tapBack()
    }

    public tapItem(evt: eui.ItemTapEvent) {
        const item = evt.item as Item
        if (!item) return
        let isSelectItem = false
        let selectedItem = null
        if (this.mail.selectItem && -1 != this.mail.selectItem.indexOf(item)) {
            selectedItem = item
            isSelectItem = true
        }
        this.item = selectedItem
        if (isSelectItem) {
            return void UIHandler.alertItemInfo(item.id)
        }
        this.doMenu(UIHandler.EVENT_NOTE, item)
    }

    public doMenu(event: number, item?: Item) {
        MailTool.doMenuButton(event, this.mail, item || this.item);
    }

    // 拒收
    public onReject() {
        this.doMenu(this.statu);
    }

    // 举报
    public onReport() {
        this.doMenu(UIHandler.EVENT_ALL_MENU_RECMAIL_REPORT);
    }

    // 回复
    public onReply() {
        this.doMenu(this.statu2);
    }

    // 提取附件
    public onAttach() {
        this.doMenu(UIHandler.EVENT_ALL_MAIL_PICK);
    }

    public onShow() {
        this.initMoney()
        this.initBag()

        let isServiceMail: boolean
        let isBackMail: boolean
        let isSendMail: boolean
        let isPlayerMail: boolean

        let hasMoney = false
        if (this._showArgs) {
            this.mail = this._showArgs
            isServiceMail = MailTool.isServiceEmail(this.mail.type)
            isBackMail = this.mail.type == MailDefine.MAIL_TYPE_BACK
            isSendMail = this.mail.type == MailDefine.MAIL_TYPE_SEND
            isPlayerMail = this.mail.type == MailDefine.MAIL_TYPE_PLAYER
            if (this.mail.money2 || this.mail.reqMoney2) {
                hasMoney = true
            }
        }
        let showBtn1 = true
        let showBtn2 = true
        let showBtnAttach = false
        let statu = UIHandler.EVENT_ALL_MAIL_DEL
        let statu2 = UIHandler.EVENT_ALL_MAIL_REPLY
        let btn1Text = "删除"
        let btn2Text = "回复"
        let title = "收邮件"

        let hasAttachItem = this.mail.isHasAttachItem()
        if (hasAttachItem) {
            showBtnAttach = true
        }

        switch (true) {
            case isBackMail:
                showBtn1 = false
                title = "回执邮件"
                if (hasAttachItem) {
                    btn2Text = "提取附件"
                    statu2 = UIHandler.EVENT_ALL_MAIL_PICK
                    showBtnAttach = false
                }
                else {
                    btn2Text = "删除"
                    statu2 = UIHandler.EVENT_ALL_MAIL_DEL
                }
                break
            case isSendMail:
                showBtn1 = false
                showBtnAttach = false
                title = "取回邮件"
                btn2Text = "收回邮件"
                statu2 = UIHandler.EVENT_ALL_MAIL_REGAIN
                break
            case isServiceMail:
                if (hasAttachItem) {
                    showBtnAttach = false
                    btn2Text = "提取附件"
                    statu2 = UIHandler.EVENT_ALL_MAIL_PICK
                }
                else {
                    showBtn1 = false
                    showBtnAttach = false
                    btn2Text = "删除"
                    statu2 = UIHandler.EVENT_ALL_MAIL_DEL
                }
                break
            default:
                if (hasAttachItem) {
                    statu = 1
                    btn1Text = "拒收邮件"
                    statu = UIHandler.EVENT_ALL_MAIL_REFUSE
                }
        }

        this.statu = statu
        this.statu2 = statu2
        this._view.txtTitle.text = title

        this._view.btnReject.label = btn1Text
        this._view.btnReply.label = btn2Text
        // 玩家发送的邮件可以被举报
        this._view.btnReport.visible = isPlayerMail
        this._view.btnReject.visible = showBtn1
        this._view.btnReply.visible = showBtn2
        this._view.btnAttach.visible = showBtnAttach

        this._view.groupReceive.x = this._view.groupSend.x = hasMoney ? 17 : 75
        this._view.moneyreceive2.visible = this._view.moneySend2.visible = hasMoney
        this._view.moneyreceive3.x = this._view.moneySend3.x = hasMoney ? this.pos3 : this._view.moneyreceive2.x
        this.updateView()
    }

    public updateView() {
        var e = this.mail,
            t = e.attachItem,
            i = e.selectItem;
        this._view.txtName.text = e.toName;
        this._view.txtMsg.textFlow = TextUtil.parseHtml(e.content);
        for (var o, n = 0, a = 0; a < this._dataForBagList.length; a++) {
            o = null;
            t ? o = t[a] : i && 0 == n && (o = i[n], n++);
            o && this._dataForBagList.replaceItemAt(o, a);
        }
        this.setMoney(this._view.moneyreceive1, e.reqMoney1);
        this.setMoney(this._view.moneyreceive2, e.reqMoney2);
        this.setMoney(this._view.moneyreceive3, e.reqMoney3);
        this.setMoney(this._view.moneySend1, e.money1);
        this.setMoney(this._view.moneySend2, e.money2);
        this.setMoney(this._view.moneySend3, e.money3);
    }

    public initMoney() {
        GUtil.initMoney([this._view.moneyreceive1, this._view.moneyreceive2, this._view.moneyreceive3, this._view.moneySend1, this._view.moneySend2, this._view.moneySend3])
    }

    public setMoney(it: CurrencyComView, val: number) {
        it.textNum.text = null != val ? "" + val : "0"
        it.enabled = false
    }

    public initBag() {
        const ary = []
        for (let i = 0; i < 300; i++) {
            ary.push(null)
        }
        this._dataForBagList.source = ary;
    }

    public tapBack() {
        UIHandler.hideView(ModName.Mail, MailViewType.MailReceive);
    }

}

