import Alert from '../../otherCmpt/alert/Alert'
import { BagEvent } from '../../bag/define/BagEvent'
import { CommonData } from '../../gameCore/CommonData'
import Facade from '../../gameCore/Facade'
import { GUtil } from '../../gameCore/GUtil'
import GameWorld from '../../GameWorld'

import Item from '../../vo/Item'
import { Layer } from '../../Layer'
import { ListBagItem2 } from '../../ListBagItem2'
import { ListBagTypeItem } from '../../ListBagTypeItem'
import MailContactPlayerView from '../view/MailContactPlayerView'
import MailProxy from '../MailProxy'
import MailTool from '../MailTool'
import { MailVo } from '../data/MailVo'
import MdrBase from '../../gameCore/MdrBase'

import MyPetVo from '../../vo/MyPetVo'
import PlayerBag from '../../bag/data/PlayerBag'
import ProtocolDefine from '../../ProtocolDefine'
import { RelationEvent } from '../../relation/define/RelationEvent'
import { Tips } from '../../otherCmpt/common/Tips'
import { Tool } from '../../gameCore/Tool'
import UIHandler from '../../UIHandler'
import { MailViewType } from '../../viewType/MailViewType'
import { ModName } from '../../viewType/ModName'
import { ProxyType } from '../../viewType/ProxyType'
import { RelationViewType } from '../../viewType/RelationViewType'
import GameNT from '../../gameCore/GameNT'
import GameText from '../../gameCore/GameText'
import { CurrencyComView } from '../../otherCmpt/common/CurrencyComView'
import { ItemIconBase } from '../../otherCmpt/common/ItemIconBase'

export default class MailContactPlayerMdr extends MdrBase<MailContactPlayerView, MailVo> {
    private _listBagTypeIndex: number
    private _proxy: MailProxy
    private _dataForBagTypeList: eui.ArrayCollection
    private _dataForBagList: eui.ArrayCollection
    private _obj: { [key: number]: boolean }
    private _selectIndex: number
    private _mail: MailVo

    selectItemIndex: number

    constructor() {
        super(Layer.window)
        this.mark('_view', MailContactPlayerView)
        this._listBagTypeIndex = -1
    }

    public onInit() {
        this._view.horizontalCenter = 0
        this._view.top = 0
        this._view.bottom = 0
        this._proxy = this.getProxy(ProxyType.Mail)
        this._view.moneyreceive1.setType(proto.ModelConst.Type.MONEY1)
        this._view.moneyreceive3.setType(proto.ModelConst.Type.MONEY3)
        this._view.moneySend1.setType(proto.ModelConst.Type.MONEY1)
        this._view.moneySend3.setType(proto.ModelConst.Type.MONEY3)
        this._view.listBagType.itemRenderer = ListBagTypeItem
        this._dataForBagTypeList = new eui.ArrayCollection(CommonData.bagTypeArray)
        this._view.listBagType.dataProvider = this._dataForBagTypeList
        this._view.listBagItem.itemRenderer = ListBagItem2
        this._dataForBagList = new eui.ArrayCollection()
        this._view.listBagItem.dataProvider = this._dataForBagList
    }

    public addListeners() {
        this.listen(this._view.checkName, this.onCheck)
        this.listen(this._view.checkID, this.onCheck)
        this.listen(this._view.btnClose, this.tapBack)
        this.listen(this._view.btnSend, this.onSend)
        this.listen(this._view.btnFriend, this.onFriend)
        this.listen(this._view.listBagType, this.tapListBayTypeItem, eui.ItemTapEvent.ITEM_TAP)
        this.listen(this._view.listBagItem, this.tapItem, eui.ItemTapEvent.ITEM_TAP)
        this.listen(this._view.scroll, this.onScroll, egret.Event.CHANGE)
        this.onNt(BagEvent.BAG_EQUIPMENT_UPDATE, this.onBagEquipemntUpdate)
        this.onNt(BagEvent.BAG_ITEM_UPDATE, this.onBagItemUpdate)
        this.onNt(BagEvent.BAG_ITEM_INPUT, this.onPut)
        this.onNt(BagEvent.BAG_ITEM_INPUT_CANCEL, this.onPutCancel)
        this.onNt(BagEvent.BAG_ITEM_DEL, this.onDel)
        this.onNt(RelationEvent.RELATION_SELECT, this.onFriendSelect)
    }

    public onScroll() {
        console.log('onScroll')
        this.updateBagSelect()
    }

    public onDel(e: GameNT<Item>) {
        const item = e.body
        item.quantity = item.totalQuantity
        this.updateBagNum(item)
        this._obj[item.slotPos] = null
        this.updateSelectItem()
        this.updateBagSelect()
    }

    public tapItem(evt: eui.ItemTapEvent) {
        const item: Item = evt.item
        this._view.listBagItem.selectedIndex = -1
        if (!item) return
        if (!this._obj[item.slotPos]) {
            if (this.isMax()) return void Tips.show('附件最多只能选择6个')
        }
        // this.setSelect(evt.itemRenderer as ListBagItem2)
        item.data = evt.itemIndex
        this._view.textSelected.text = item.name
        const itemClone = item.clone(true)
        itemClone.quantity = itemClone.totalQuantity
        UIHandler.inputItem(itemClone, true)
    }

    private onPut(nt: GameNT<Item>) {
        const item = nt.body
        const itemClone = item.clone(true)
        itemClone.quantity = item.totalQuantity - item.quantity
        this.updateBagNum(itemClone)
        let index = this.selectItemIndex
        if (this._obj[item.slotPos]) {
            index = this.findPut(item)
        }
        else {
            this._obj[item.slotPos] = true
            this.updateBagSelect()
        }
        const iconNode = this.getItem(index)
        if (iconNode) {
            iconNode.data = item
            iconNode.img_delete.visible = true
            this.updateSelectItem()
        }
    }

    private onPutCancel(nt: GameNT<Item>) {
        const item = nt.body
        if (!this._obj[item.slotPos]) return
        this.updateBagSelect()
    }

    public updateBagSelect() {
        for (let i = 0; i < this._view.listBagItem.numChildren; i++) {
            const it = this._view.listBagItem.getChildAt(i) as ListBagItem2
            const data = it.data
            it.img_selected.visible = data ? this._obj[data.slotPos] : false
        }
    }

    public updateBagNum(item: Item) {
        this._dataForBagList.replaceItemAt(item, item.data)
    }

    public findPut(item: Item) {
        for (let i = 0; i < 6; i++) {
            const it = this.getItem(i)
            const data = it.data
            if (data && data.slotPos == item.slotPos) return i
        }
    }

    public isMax() {
        let total = 0
        for (let i = 0; i < 6; i++) {
            const it = this.getItem(i)
            const data = it.data
            data && total++
        }
        return 6 == total
    }

    public setSelect(it: ListBagItem2, cancel: boolean = false) {
        const item: Item = it.data
        if (this._obj[item.slotPos]) {
            cancel = false
        }
        it.img_selected.visible = !cancel
    }

    // 附件列表
    public getItemList() {
        const ary: Item[] = []
        for (let i = 0; i < 6; i++) {
            const ibNode = this.getItem(i)
            const item = ibNode.data
            item && ary.push(item)
        }
        return ary
    }

    public onBagEquipemntUpdate() {
        this.updateBag()
    }

    public onBagItemUpdate() {
        this.updateBag()
    }

    public tapListBayTypeItem() {
        var e = this._view.listBagType.selectedIndex
        e == this._listBagTypeIndex && (this._view.listBagType.selectedIndex = -1)
        this._listBagTypeIndex = this._view.listBagType.selectedIndex
        this.updateBag()
    }

    public updateBag() {
        const selectedBagType = this._view.listBagType.selectedIndex
        const plr = GameWorld.myPlayer
        const bag = plr.bag
        const bagSize = plr.BagSize
        const ary: Item[] = []
        let pos = 0
        for (let r = 0; bagSize > r; r++) {
            const item = bag.getItem(PlayerBag.BAG_START + r)
            if (item) {
                item.totalQuantity = item.quantity
                ++pos
            }
            if (-1 == selectedBagType) {
                ary.push(item)
            } else if (item && item.getItemType() == selectedBagType) {
                ary.push(item)
            }
        }
        for (let i = 0; i < this._dataForBagList.length; i++) {
            const item = ary[i] as Item
            this._dataForBagList.replaceItemAt(item ? item.clone(true) : null, i)
        }
        this.updateBagSelect()
    }

    public initView() {
        this.listBagTypeRenderer()
        this._view.listBagType.selectedIndex = -1
        this._view.textSelected.text = ''
        const plr = GameWorld.myPlayer
        const bag = plr.bag
        const ary: Item[] = []
        for (let i = 0; plr.BagSize > i; i++) {
            const item = bag.getItem(PlayerBag.BAG_START + i)
            ary.push(item)
        }
        this._dataForBagList.source = GUtil.getMinList(ary, 300)
    }

    public listBagTypeRenderer() {
        this._dataForBagTypeList.source = CommonData.bagTypeArray
    }

    public onFriend() {
        UIHandler.showWin(ModName.Relation, RelationViewType.RelationMain, {
            subIdx: 1,
            subParams: {
                isMail: true
            }
        })
    }

    public onFriendSelect(e) {
        var t = e.body
        this._view.txtPlayer.text = t.getClientUid()
        this._selectIndex = ProtocolDefine.MAIL_SEND_UID
        this.updateSelect()
    }

    public async onSend() {
        if (!this._view.txtPlayer.text) {
            return void UIHandler.alertMessage(GameText.STR_MAIL_NO_NAME)
        }

        if (!this._view.txtMsg.text) {
            return void UIHandler.alertMessage(GameText.STR_NULL_ENTER)
        }

        const moneyAry = [this._view.moneyreceive1, this._view.moneyreceive3, this._view.moneySend1, this._view.moneySend3]
        if (!this.dealInput(moneyAry)) {
            return void UIHandler.alertMessage("金钱输入过大")
        }

        const attachmentList = this.getItemList()
        for (let i = 0; i < attachmentList.length; i++) {
            const item = attachmentList[i]
            if (item.isBinded()) {
                return void UIHandler.alertMessage("物品已经绑定")
            }
            if (item.isNotOperate()) {
                return void UIHandler.alertMessage("物品无法操作")
            }
            const is = await MyPetVo.getItemPetEquip(item)
            if (is) {
                return void UIHandler.alertMessage(GameText.STR_PET_IS_EQUIP)
            }
        }

        const mail = new MailVo()
        mail.sendId = this._showArgs.sendId
        mail.toName = this._view.txtPlayer.text
        mail.content = this._view.txtMsg.text
        mail.reqMoney1 = this.getNum(this._view.moneyreceive1)
        mail.reqMoney3 = this.getNum(this._view.moneyreceive3)
        mail.money1 = this.getNum(this._view.moneySend1)
        mail.money3 = this.getNum(this._view.moneySend3)
        mail.attachItem = attachmentList
        this._mail = mail

        if (!await Tool.checkEnoughMoney(mail.money1, 0, mail.money3)) {
            return void UIHandler.errorMessage("金钱不足!")
        }

        const send = () => {
            const sure = mail.isPayMoneySure()
            if (sure) {
                return void UIHandler.alertMessage(sure, null, Handler.alloc(this, this.sendMail))
            }
            this.sendMail()
        }
        send()
    }

    // 检查货币输入值
    public dealInput(inputMpneyAry: CurrencyComView[]) {
        for (let i = 0; i < inputMpneyAry.length; i++) {
            const it = inputMpneyAry[i]
            if (it.textNum.text) {
                const val = parseInt(it.textNum.text)
                if (val >= Tool.MAX_VALUE_int) return false
            }
        }
        return true
    }

    public getNum(it: CurrencyComView) { return GUtil.getMoney(it) }

    public setNum(it: CurrencyComView) { GUtil.setMoney(it) }

    // 发送邮件
    public sendMail() {
        const type = this._showArgs.isId ? ProtocolDefine.MAIL_SEND_UID : this._selectIndex
        MailTool.doMailSendMsg(this._mail, type)
        this._mail = null
    }

    public onCheck(evt) {
        this._selectIndex = ProtocolDefine.MAIL_SEND_NAME
        if (evt.currentTarget == this._view.checkID) {
            this._selectIndex = ProtocolDefine.MAIL_SEND_UID
        }
        this.updateSelect()
    }

    public updateSelect() {
        const isById = this._selectIndex == ProtocolDefine.MAIL_SEND_UID
        this._view.checkName.selected = this._selectIndex == ProtocolDefine.MAIL_SEND_NAME
        this._view.checkID.selected = isById
        this._view.txtPlayer.text = isById ? this._showArgs.sendId + "" : this._showArgs.toName
    }

    public initMoney() {
        GUtil.initMoney([this._view.moneyreceive1, this._view.moneyreceive3, this._view.moneySend1, this._view.moneySend3])
    }

    public updateSelectItem() {
        if (null != this.selectItemIndex) {
            return void this.findSelectItem()
        }
        this.selectItemIndex = 0
    }

    public findSelectItem() {
        for (let i = 0; i < 6; i++) {
            const item = this.getItem(i)
            const data = item.data
            if (!data) {
                this.selectItemIndex = i
                break
            }
        }
    }

    public getItem(index: number): ItemIconBase { return this._view['item' + index] }

    public onShow() {
        this._obj = {}
        let isById = this._showArgs?.isId
        if (this._showArgs) {
            this._view.txtPlayer.text = isById ? this._showArgs.sendId + "" : this._showArgs.toName
        }
        this.initMoney()
        this.updateSelectItem()
        this._selectIndex = isById ? ProtocolDefine.MAIL_SEND_UID : ProtocolDefine.MAIL_SEND_NAME
        this.updateSelect()
        this.initView()
        this.updateBag()
    }

    public onHide() {
        this.clearDel()
        super.onHide()
    }

    public clearDel() {
        for (let i = 0; i < 6; i++) {
            const item = this.getItem(i)
            item.clearDel()
        }
        this._view.txtMsg.text = ''
        this.setNum(this._view.moneyreceive1)
        this.setNum(this._view.moneyreceive3)
        this.setNum(this._view.moneySend1)
        this.setNum(this._view.moneySend3)
    }

    public tapBack() {
        UIHandler.hideView(ModName.Mail, MailViewType.MailContactPlayer)
    }
}
