import ItemRendererBase from '../../otherCmpt/common/ItemRendererBase'
import { ItemIconBase } from '../../otherCmpt/common/ItemIconBase'
import Facade from '../../gameCore/Facade'
import { ModName } from '../../viewType/ModName'
import { ProxyType } from '../../viewType/ProxyType'
import MailDefine from '../define/MailDefine'
import TimeMgr from '../../gameCore/TimeMgr'
import { Tool } from '../../gameCore/Tool'
import MailTool from '../MailTool'
import facade from '../../gameCore/Facade'
import { MailVo } from '../data/MailVo'

export default class MailListItemView extends ItemRendererBase<MailVo> {
    public imgBg: eui.Image
    public txtTitle: eui.Label
    public txtTime: eui.Label
    public groupNew: eui.Group
    public groupRead: eui.Group
    public groupDel: eui.Group
    public item: ItemIconBase
    public imgSelect: eui.Image
    public lbEenTime: eui.Label
    selectState: boolean
    tx: number

    constructor() {
        super()
        this.skinName = 'MailListItemSkin'
        this.tx = this.txtTitle.x
    }

    get proxy() {
        return facade.getProxy(ModName.Mail, ProxyType.Mail)
    }

    public dataChanged() {
        if (!this.data) return
        var e = this.data,
            t = e.type == MailDefine.MAIL_TYPE_MONEY
        this.imgSelect.visible = t && this.proxy.isSelectDelete
        this.proxy.isSelectDelete || ((this.selectState = false), (this.imgSelect.source = 'common_btn_xuanxiang'))
        var i = e.getStatus(),
            o = i == MailDefine.MAIL_STATU_DEL,
            a = e.status <= MailDefine.MAIL_STATUS_UNREAD_TRADE || e.status == MailDefine.MAIL_STATUS_READ_ITEM || o,
            s = e.endTime.toNumber() - TimeMgr.timer.serverTime
        this.lbEenTime.visible = (t && a) || 0 >= s ? false : true
        var u = Math.floor(s / Tool.MILLISECOND_IN_DAY)
        this.lbEenTime.textColor = u > 3 ? 7271780 : 15496822
        this.lbEenTime.text = '有效期：' + this.getEndTime(e.endTime) + '天'
        this.txtTime.verticalCenter = this.lbEenTime.visible ? -17 : 0
        this.txtTitle.text = e.title
        this.txtTitle.y = (this.height - this.txtTitle.textHeight) >> 1
        var _ = e.datetime,
            d = _ ? _.indexOf(' ') : -1
            ; -1 != d && (_ = _.substring(0, d)), (_ = _.replace(/\-/g, '/'))
        this.txtTime.text = _
        var c = i == MailDefine.MAIL_STATU_NEW,
            p = i == MailDefine.MAIL_STATU_READ
        this.groupNew.visible = c
        this.groupRead.visible = p
        this.groupDel.visible = o
        var h = e.isHasAttachItem(true)
        this.item.visible = h
        this.txtTitle.x = h ? this.tx : this.item.x
        h && (this.item.data = e.attachItem[0])
        var f = 1
        o && (f = 0.5)
        this.alpha = f
    }

    public getEndTime(time: Long | number) {
        time = time instanceof Long ? time.toNumber() : time
        let diff = time - TimeMgr.timer.serverTime
        if (0 >= diff) return ''
        let minute = diff / Tool.MILLISECOND_IN_MINUTE
        diff % Tool.MILLISECOND_IN_MINUTE > 0 && minute++
        let day = Math.floor(minute / Tool.MINUTE_IN_DAY)
        return 1 > day ? '不足1' : day + ''
    }

    public onAddToStage() {
        this.imgSelect.addEventListener(egret.TouchEvent.TOUCH_TAP, this.tapSelect, this)
        this.imgBg.addEventListener(egret.TouchEvent.TOUCH_TAP, this.tapBg, this)
    }

    public onRemoveFromStage() {
        this.imgSelect.removeEventListener(egret.TouchEvent.TOUCH_TAP, this.tapSelect, this)
        this.imgBg.removeEventListener(egret.TouchEvent.TOUCH_TAP, this.tapBg, this)
    }

    public tapSelect(evt: eui.ItemTapEvent) {
        if (!this.data) return
        this.selectState = !this.selectState
        this.imgSelect.source = this.selectState ? 'common_btn_xuanxiang_d' : 'common_btn_xuanxiang'
        evt.stopPropagation()
        let id = this.data.id.toNumber()
        const tmp = this.proxy.deleteMails[id]
        if (this.selectState && !tmp) {
            this.proxy.deleteMails[id] = this.data
            return
        }
        if (tmp) {
            delete this.proxy.deleteMails[id]
        }
    }

    public tapBg() {
        if (!this.data) return
        MailTool.doMailDetailMsg(this.data)
    }
}
