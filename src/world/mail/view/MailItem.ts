import facade from '../../gameCore/Facade'
import Facade from '../../gameCore/Facade'
import { Tool } from '../../gameCore/Tool'
import ItemRendererBase from '../../otherCmpt/common/ItemRendererBase'
import { ModName } from '../../viewType/ModName'
import { ProxyType } from '../../viewType/ProxyType'

export default class MailItem extends ItemRendererBase<number> {
    txtTitle: eui.Label
    groupNum: eui.Group
    txtNum: eui.Label
    lbRead: eui.Label

    constructor() {
        super()
        this.skinName = 'MailItemSkin'
    }

    public get model() {
        return facade.getProxy(ModName.Mail, ProxyType.Mail).data
    }

    public dataChanged() {
        if (!this.data) return
        const type = this.data
        const proxy = facade.getProxy(ModName.Mail, ProxyType.Mail)
        const info = proxy.mailNums[type]
        if (info) {
            this.lbRead.visible = info.read + info.unRead > 0
            this.lbRead.text = Tool.manageString('未读信息:%U\n已读信息:%U', [info.unRead + '', info.read + ''])
        } else {
            this.lbRead.visible = false
        }

        let r = this.model.getMailNumIndex(type)
        let s = this.model.getMailNum(r, 0)
        let l = this.model.getTypeName(type)
        this.txtTitle.text = l
        if (s > 0) {
            this.txtNum.text = s > 99 ? s + '+' : s + ''
        }
        this.groupNum.visible = s > 0
    }
}
