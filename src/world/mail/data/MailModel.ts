import MailDefine from '../define/MailDefine'
import { MailEvent } from '../define/MailEvent'
import UIHandler from '../../UIHandler'
import { MailViewType } from '../../viewType/MailViewType'
import { MainViewType } from '../../viewType/MainViewType'
import { ModName } from '../../viewType/ModName'
import facade from '../../gameCore/Facade'
import { MailVo } from './MailVo'

export class MailModel {
    private _obj: { [key: number]: [Vector<MailVo>, number] }
    receiveList: number[]
    sendList: number[]
    otherList: number[]
    receiveNameList: string[]
    sendNameList: string[]
    otherNameList: string[]
    selectType: any

    constructor() {
        this._obj = {}
        this.receiveList = [MailDefine.MAIL_TYPE_PLAYER, MailDefine.MAIL_TYPE_TASK, MailDefine.MAIL_TYPE_MONEY, MailDefine.MAIL_TYPE_SYSTEM]
        this.sendList = [MailDefine.MAIL_SEND_TYPE_PLAYER, MailDefine.MAIL_SEND_TYPE_SERVICE]
        this.otherList = [MailDefine.MAIL_TYPE_SEND, MailDefine.MAIL_TYPE_BACK]
        this.receiveNameList = ['玩家邮件', '任务奖励邮件', '充值邮件', '客服系统邮件']
        this.sendNameList = ['联系玩家', '联系客服']
        this.otherNameList = ['已发交易邮件', '回执/退回邮件']
        this.dealTypeNameList(this.receiveList, this.receiveNameList)
        this.dealTypeNameList(this.sendList, this.sendNameList)
        this.dealTypeNameList(this.otherList, this.otherNameList)
    }

    public dealTypeNameList(e, t) {
        for (var i, o = e.length, n = 0; o > n; n++) {
            i = e[n]
            this.setTypeName(i, t[n])
        }
    }

    public getTypeName(e) {
        return this._obj['typeName_' + e]
    }

    public setTypeName(e, t) {
        this._obj['typeName_' + e] = t
    }

    public setMailList(type: proto.MailDefine.MAIL_TYPE, data: [Vector<MailVo>, number]) {
        this._obj[type] = data
    }

    public getMailList(type: proto.MailDefine.MAIL_TYPE) {
        return this._obj[type] || []
    }

    public getMailNum(index: number, t?: number) { return this._obj['count' + index] || t }

    public setMailNum(index: number, num: number) { this._obj['count' + index] = num }

    public removeNewMailNum(type: proto.MailDefine.MAIL_TYPE) {
        let idx: number
        switch (type) {
            case proto.MailDefine.MAIL_TYPE.PLAYER:
                idx = 0
                break
            case proto.MailDefine.MAIL_TYPE.TASK:
                idx = 1
                break
            case proto.MailDefine.MAIL_TYPE.MONEY:
                idx = 2
                break
            case proto.MailDefine.MAIL_TYPE.SERVICE | proto.MailDefine.MAIL_TYPE.SYSTEM:
                idx = 3
                break
            case proto.MailDefine.MAIL_TYPE.BACK | proto.MailDefine.MAIL_TYPE.RECEIPT:
                idx = 4
        }
        if (idx == null) return
        let num = this.getMailNum(idx)
        this.setMailNum(idx, num - 1)
        this.updateRp()
    }

    public getMailNumIndex(type: proto.MailDefine.MAIL_TYPE) {
        switch (type) {
            case proto.MailDefine.MAIL_TYPE.PLAYER:
                return 0
            case proto.MailDefine.MAIL_TYPE.TASK:
                return 1
            case proto.MailDefine.MAIL_TYPE.MONEY:
                return 2
            case proto.MailDefine.MAIL_TYPE.SERVICE:
            case proto.MailDefine.MAIL_TYPE.SYSTEM:
                return 3
            case proto.MailDefine.MAIL_TYPE.BACK:
            case proto.MailDefine.MAIL_TYPE.RECEIPT:
                return 4
        }
        return -1
    }

    public getWin(type: proto.MailDefine.MAIL_TYPE, getTitle?: boolean) {
        let viewType: MailViewType = MailViewType.MailList
        let title = '邮件'
        switch (type) {
            case proto.MailDefine.MAIL_TYPE.PLAYER:
                title = '玩家邮件'
                break
            case proto.MailDefine.MAIL_TYPE.TASK:
                title = '任务奖励邮件'
                break
            case proto.MailDefine.MAIL_TYPE.MONEY:
                title = '充值邮件'
                break
            case proto.MailDefine.MAIL_TYPE.SERVICE:
            case proto.MailDefine.MAIL_TYPE.SYSTEM:
                title = '客服/系统邮件'
                break
            case proto.MailDefine.MAIL_TYPE.SEND:
                title = '交易邮件'
                break
            case proto.MailDefine.MAIL_TYPE.RECEIPT:
            case proto.MailDefine.MAIL_TYPE.BACK:
                title = '回退/回执邮件'
                break
            case proto.MailDefine.MAIL_TYPE.SEND_PLAYER:
                title = '联系玩家'
                viewType = MailViewType.MailContactPlayer
                break
            case proto.MailDefine.MAIL_TYPE.SEND_SERVICE:
                viewType = null
                this.openService()
        }
        return getTitle ? title : viewType
    }

    public openService() {
        // if (go.isHKTNative || go.targetPf == TARGET_PLATFORM.HKT) {
        //     var e = Tool.getStyle(GameWorld.myPlayer),
        //         t = LoadMgr.ins.getRealUrl(e, '.png')
        //     return void (gs.showCustomerService && gs.showCustomerService(null, null, null, t, GameWorld.totalCharge))
        // }
        go.openPay
            ? facade.showView(ModName.Main, MainViewType.SEARCH, {
                title: '联系客服',
                tag: '请输入内容：',
                list: ['咨询', '申诉', '充值问题', '建议反馈'],
                alpha: 0.95
            })
            : facade.showView(ModName.Main, MainViewType.SEARCH, {
                title: '联系客服',
                tag: '请输入内容：',
                list: ['咨询', '申诉', '建议反馈'],
                alpha: 0.95
            })
    }

    public openWin(type: proto.MailDefine.MAIL_TYPE) {
        let title = this.getWin(type)
        UIHandler.showWin(ModName.Mail, title, type)
    }

    public updateRp() {
        let isRp = false
        for (let i = 0; 6 > i; i++)
            if (this.getMailNum(i) > 0) {
                isRp = true
                break
            }
        facade.sendNt(MailEvent.MAIL_RP, isRp)
    }
}
