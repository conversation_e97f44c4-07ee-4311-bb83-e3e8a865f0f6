import { GameUtil } from "../../gameCore/GameUtil";
import { Tool } from "../../gameCore/Tool";
import StringBuffer from "../../gameCore/StringBuffer";
import MailDefine from "../define/MailDefine";
import GameText from "../../gameCore/GameText";
import PowerString from "../../gameCore/PowerString";
import Mission from "../../mission/data/Mission";
import Item from "../../vo/Item";
import TimeUtil from "../../gameCore/TimeUtil";
import Utilities from "../../gameCore/Utilities";

export class MailVo {

    public id: Long
    public title: string
    public needcheck: boolean
    public endTime: Long

    // 目标的名称
    public toName: string;
    public status: number;
    public type: proto.MailDefine.MAIL_TYPE;
    // 目标的id
    public sendId: number;
    public isId: boolean;

    public intValue1: number = 0
    public newMailNum: number[] = null
    public selectItem: Item[] = null
    public attachItem: Item[] = null
    public money1: number = 0
    public money2: number = 0
    public money3: number = 0
    public reqMoney1: number = 0
    public reqMoney2: number = 0
    public reqMoney3: number = 0
    public content: string = ""
    public sendcontent: string = ""

    public sender: number

    // 用来标记是不是拉取过详情了
    public isDetail: boolean = false

    constructor() {
        this.newMailNum = new Array(5)
    }

    get clone() {
        return GameUtil.clone(this)
    }

    get datetime() { return TimeUtil.formatTime(this.endTime.toNumber() - Utilities.MILLISECOND_IN_DAY * 180, "yyyy/MM/dd") }

    public isTypeBit(type: proto.MailDefine.MAIL_TYPE) {
        return 0 != (this.type & type);
    }

    public setTabStatus(is: boolean, bit: number) {
        this.intValue1 = Tool.setBit(is, bit, this.intValue1);
    }

    public isTabStatus(bit: number) {
        return Tool.isBit(bit, this.intValue1);
    }

    public isHasSelectItem() {
        if (null == this.selectItem) return false
        if (this.selectItem.length <= 0) return false
        for (let i = 0; i < this.selectItem.length; i++) {
            if (null != this.selectItem[i]) return true
        }
        return false
    }

    public getTitleDesc() {
        const e = new StringBuffer();
        switch (this.isTypeBit(MailDefine.MAIL_TYPE_SEND) ? e.append(Tool.manageString(GameText.STR_MAIL_TITLE_TYPE_SEND, this.toName)) : this.isTypeBit(MailDefine.MAIL_TYPE_BACK) ? e.append(Tool.manageString(GameText.STR_MAIL_TITLE_TYPE_BACK, this.toName)) : e.append(Tool.manageString(GameText.STR_MAIL_TITLE_TYPE_OTHER, this.toName)), this.status) {
            case MailDefine.MAIL_STATUS_READ_TEXT:
            case MailDefine.MAIL_STATUS_UNREAD_TEXT:
                e.append(GameText.getText(GameText.TI_MAIL));
                break;
            case MailDefine.MAIL_STATUS_READ_ITEM:
            case MailDefine.MAIL_STATUS_UNREAD_ITEM:
                e.append(GameText.getText(GameText.TI_MAIL));
                break;
            case MailDefine.MAIL_STATUS_READ_TRADE:
            case MailDefine.MAIL_STATUS_UNREAD_TRADE:
                e.append(GameText.STR_MAIL_READ_MAIL);
                break;
            default:
                e.append(GameText.STR_MAIL_UNKNOWN_MAIL);
        }
        return e.toString();
    }

    public isPayMoneySure() {
        const buffer = new StringBuffer()
        if (this.attachItem?.length >= 0) {
            for (let i = 0; i < this.attachItem.length; i++) {
                const item = this.attachItem[i]
                if (!item) continue
                buffer.append(Tool.manageString(GameText.STR_MAIL_AFFIX, item.getRewardDesc()))
            }
        }
        if (this.money1 > 0) {
            buffer.append(PowerString.makeColorString(Tool.manageString(GameText.STR_MAIL_PRESENT, GameText.STR_MONEY1 + this.money1), Tool.COLOR_YELLOW))
        }
        if (this.money2 > 0) {
            buffer.append(PowerString.makeColorString(Tool.manageString(GameText.STR_MAIL_PRESENT, GameText.STR_MONEY2 + this.money2), Tool.COLOR_YELLOW) + "\n")
        }
        if (this.money3 > 0) {
            buffer.append(PowerString.makeColorString(Tool.manageString(GameText.STR_MAIL_PRESENT, GameText.STR_MONEY3 + this.money3), Tool.COLOR_YELLOW) + "\n")
        }
        if (Tool.isNullText(buffer.toString())) return ""
        if (this.reqMoney1 <= 0 && this.reqMoney2 <= 0 && this.reqMoney3 <= 0) {
            return buffer.toString() + GameText.STR_MAIL_SEND_ASK;
        }
        return ""
    }

    public getContent() {
        return this.content;
    }

    public isHasReqMoney(buffer?: StringBuffer) {
        buffer = buffer || new StringBuffer()
        if (this.reqMoney1 <= 0 && this.reqMoney2 <= 0 && this.reqMoney3 <= 0) {
            return false
        }
        let str = ""
        const req = (val: number, name: string) => {
            if (val <= 0) return
            str += PowerString.makeColorString(name + " -" + val, Tool.COLOR_RED) + " "
        }
        req(this.reqMoney1, GameText.STR_MONEY1)
        req(this.reqMoney2, GameText.STR_MONEY2)
        req(this.reqMoney3, GameText.STR_MONEY3)
        if (!Tool.isNullText(str)) {
            buffer.append(Tool.manageString(GameText.STR_MAIL_PAY, str))
        }

        str = ""
        const present = (val: number, name: string) => {
            if (val <= 0) return
            str += name + " +" + val + " "
        }
        present(this.money1, GameText.STR_MONEY1)
        present(this.money2, GameText.STR_MONEY2)
        present(this.money3, GameText.STR_MONEY3)

        if (!Tool.isNullText(str)) {
            buffer.append(Tool.manageString(GameText.STR_MAIL_PRESENT, str))
        }

        str = ""
        const checkPay = (get: number, req: number, name: string) => {
            let diff = get - req
            if (diff == 0) return
            const sign = diff < 0 ? " " + diff : " +" + diff
            const color = diff < 0 ? Tool.COLOR_RED : Tool.COLOR_GOLD_YELLOW
            str += PowerString.makeColorString(name + sign + " ", color)
        }
        checkPay(this.money1, this.reqMoney1, GameText.STR_MONEY1)
        checkPay(this.money2, this.reqMoney2, GameText.STR_MONEY2)
        checkPay(this.money3, this.reqMoney3, GameText.STR_MONEY3)
        if (!Tool.isNullText(str)) {
            buffer.append(Tool.manageString(GameText.STR_MAIL_GROSS, str))
        }
        return true
    }

    public getToName() {
        return this.toName;
    }

    public setToName(e) {
        this.toName = e;
    }

    public isDirectAttach() {
        if (this.reqMoney1 > 0 || this.reqMoney2 > 0 || this.reqMoney3 > 0) return false;
        if (this.isHasSelectItem()) return false;
        if (this.money1 > 0 || this.money2 > 0 || this.money3 > 0) return true;
        if (null != this.attachItem && this.attachItem.length >= 0) for (var e = 0; e < this.attachItem.length; e++) if (null != this.attachItem[e]) return true;
        return false;
    }

    public addAttachItem(item) {
        if (null == item) return false;
        null == this.attachItem && (this.attachItem = new Array(MailDefine.MAIL_MAX_ATTACHMENT));
        for (let t = 0; t < this.attachItem.length; t++) {
            if (null == this.attachItem[t]) {
                this.attachItem[t] = item
                return true
            }
        }
        return false;
    }

    public setAttachItem(e, t) {
        null == this.attachItem && (this.attachItem = new Array(MailDefine.MAIL_MAX_ATTACHMENT));
        if (0 > t || t > this.attachItem.length) {
            return false
        }
        this.attachItem[t] = e
        return true
    }

    public isSameAttachItem(e) {
        if (null == e) return -1;
        if (null == this.attachItem) return -1;
        for (let t = 0; t < this.attachItem.length; t++) {
            if (null != this.attachItem[t] && this.attachItem[t].slotPos == e.slotPos && this.attachItem[t].id == e.id) {
                return t;
            }
        }
        return -1;
    }

    /**
     * 检查邮件是不是有附件
     * @param ignoreMoney 是否忽略金钱
     * @returns 
     */
    public isHasAttachItem(ignoreMoney: boolean = false) {
        if (this.attachItem?.length > 0) {
            for (let i = 0; i < this.attachItem.length; i++) {
                if (this.attachItem[i]) {
                    return true
                }
            }
        }
        if (ignoreMoney) return false
        return this.money1 > 0 || this.money2 > 0 || this.money3 > 0 || this.reqMoney1 > 0 || this.reqMoney2 > 0 || this.reqMoney3 > 0
    }

    public getMissionRewardDesc() {
        const e = new StringBuffer();
        e.append(Mission.convertDesc(this.content));
        e.append("\n");
        const t = new Mission(-1);
        t.money2 = this.money2;
        t.money3 = this.money3;
        t.rewardItems = this.attachItem;
        e.append(t.getMissionRewardString(false, this.money1));
        return e.toString();
    }

    public getStatusDesc() {
        if (this.isTabStatus(MailDefine.DEL_STATUS))
            return "[" + GameText.STR_MAIL_HAS_DEL + "]";
        switch (this.status) {
            case MailDefine.MAIL_STATUS_UNREAD_TEXT:
            case MailDefine.MAIL_STATUS_UNREAD_ITEM:
            case MailDefine.MAIL_STATUS_UNREAD_TRADE:
                return "(" + GameText.STR_MAIL_NEW + ")";
            case MailDefine.MAIL_STATUS_READ_TEXT:
            case MailDefine.MAIL_STATUS_READ_ITEM:
            case MailDefine.MAIL_STATUS_READ_TRADE:
                return "[" + GameText.STR_MAIL_HAS_READ + "]";
        }
        return "";
    }

    public getStatus() {
        if (this.isTabStatus(MailDefine.DEL_STATUS)) return MailDefine.MAIL_STATU_DEL
        if (this.status == MailDefine.MAIL_STATUS_READ_NO_ITEM) return MailDefine.MAIL_STATU_READ
        switch (this.status) {
            case MailDefine.MAIL_STATUS_UNREAD_TEXT:
            case MailDefine.MAIL_STATUS_UNREAD_ITEM:
            case MailDefine.MAIL_STATUS_UNREAD_TRADE:
                return MailDefine.MAIL_STATU_NEW
            case MailDefine.MAIL_STATUS_READ_TEXT:
            case MailDefine.MAIL_STATUS_READ_ITEM:
            case MailDefine.MAIL_STATUS_READ_TRADE:
                return MailDefine.MAIL_STATU_READ;
        }
        return MailDefine.MAIL_STATU_NEW
    }

    public setReadStatus() {
        switch (this.status) {
            case MailDefine.MAIL_STATUS_UNREAD_TEXT:
                this.status = MailDefine.MAIL_STATUS_READ_TEXT;
                break;
            case MailDefine.MAIL_STATUS_UNREAD_ITEM:
                this.status = MailDefine.MAIL_STATUS_READ_ITEM;
                break;
            case MailDefine.MAIL_STATUS_UNREAD_TRADE:
                this.status = MailDefine.MAIL_STATUS_READ_TRADE;
        }
    }


}

