import { <PERSON><PERSON><PERSON><PERSON> } from './viewType/ModName'
import { ProxyType } from './viewType/ProxyType'
import { ShopViewType } from './viewType/ShopViewType'
import { ChatViewType } from './viewType/ChatViewType'
import { ActivityViewType } from './viewType/ActivityViewType'
import { TeamViewType } from './viewType/TeamViewType'
import { MainViewType } from './viewType/MainViewType'
import { MainEvent } from './main/define/MainEvent'
import { GoodsViewType } from './viewType/GoodsViewType'
import { MissionViewType } from './viewType/MissionViewType'
import { BagViewType } from './viewType/BagViewType'
import { PetViewType } from './viewType/PetViewType'
import { RaidersViewType } from './viewType/RaidersViewType'
import { MailViewType } from './viewType/MailViewType'
import { RoleViewType } from './viewType/RoleViewType'
import { AchievementType } from './viewType/AchievementType'
import { LeaderboardType } from './viewType/LeaderboardType'
import { RelationViewType } from './viewType/RelationViewType'
import { BattleViewType } from './viewType/BattleViewType'
import { UserVip } from './UserVip'
import { FurnaceViewType } from './viewType/FurnaceViewType'
import { EscortViewType } from './viewType/EscortViewType'
import { EscortEvent } from './escort/define/EscortEvent'
import { ChargeType } from './viewType/ChargeType'
import { DigHoleViewType } from './viewType/DigHoleViewType'
import { ArenaViewType } from './viewType/ArenaViewType'
import { ChiselSealViewType } from './viewType/ChiselSealViewType'
import { CountryViewType } from './viewType/CountryViewType'
import Alert from './otherCmpt/alert/Alert'
import { SystemViewType } from './viewType/SystemViewType'

import GameText from './gameCore/GameText'
import { Layer } from './Layer'
import UIStack from './UIStack'
import { Tool } from './gameCore/Tool'

import GameText2 from './gameCore/GameText2'
import { Define } from './gameCore/Define'
import Achieve from './achievement/data/Achieve'
import { UIDefine } from './gameCore/UIDefine'
import ListPlayerVo from './vo/ListPlayerVo'
import Item from './vo/Item'
import BattleLogic from './battle/BattleLogic'
import LevelOpen from './LevelOpen'
import GameWorld from './GameWorld'
import City from './city/data/City'
import { InputAlertData } from './InputAlertData'
import PetComposite from './pet/data/PetComposite'
import TeamBoss from './escort/data/TeamBoss'
import { TutorialData } from './gameCore/TutorialData'
import ModelVo from './vo/ModelVo'
import SpecialIconUtil from './gameCore/SpecialIconUtil'
import Escort from './escort/data/Escort'
import NewEscort from './escort/data/NewEscort'
import StringBuffer from './gameCore/StringBuffer'
import Utilities from './gameCore/Utilities'
import PowerString from './gameCore/PowerString'
import ActivitySignIn from './activity/data/ActivitySignIn'
import MyPetVo from './vo/MyPetVo'
import MsgHandler from './MsgHandler'
import Enchant from './bag/data/Enchant'
import facade from './gameCore/Facade'
import PlayerVo from './vo/PlayerVo'
import { ShowViewOptions } from './viewType/ViewParamsMap'

export default class UIHandler {
    public static delayData: any
    public static isKey: boolean

    public static errorMessage(content: string) {
        Alert.confirm(content, null, '错误')
    }

    public static alertMessage(content: string, title = '温馨提示', confirmCallback?, determineText?, cancelCallback?, cancelText?, guideId?, data?, isEasy?) {
        Alert.confirm(content, confirmCallback, title, determineText, cancelCallback, cancelText, null, guideId, data, isEasy)
    }

    public static alertReMindMessage(content: string, remindType, title = '温馨提示', confirmCallback?, determineText?, cancelCallback?, cancelText?, guideId?, data?) {
        Alert.confirmParam({
            content,
            title,
            confirm: confirmCallback,
            determine: determineText,
            cancelCb: cancelCallback,
            cancel: cancelText,
            guideId,
            data,
            remindType
        })
    }

    public static alertMessageGuide(content: string, guideId) {
        Alert.confirmParam({ content, guideId })
    }

    public static formatAlertMessage(title = '温馨提示', content: any, confirmCallback?, determineText?, cancelCallback?, cancelText?) {
        facade.showView(ModName.Main, MainViewType.FormatAlert, {
            title,
            content,
            confirm: confirmCallback,
            determine: determineText,
            cancelCb: cancelCallback,
            cancel: cancelText
        })
    }

    public static async waitForTwiceSureUI(content: string, title = '温馨提示', determineText = '确定', cancelText = '取消', guideId?, isEasy?: boolean) {
        const self = this
        return await new Promise(function (resolve, reject) {
            const confirmCallback = Handler.alloc(null, function () {
                return resolve(true)
            })
            const cancelCallback = Handler.alloc(null, function () {
                return resolve(false)
            })
            self.alertMessage(content, title, confirmCallback, determineText, cancelCallback, cancelText, guideId, null, isEasy)
        })
    }

    public static async asyShopBuy(money: number, buyNum: number, buyMoney: number, content: string, moneyDesc = '黄金', title = '温馨提示') {
        return await new Promise(function (resolve, reject) {
            facade.showView(ModName.Main, MainViewType.MainShopBuy, {
                title: title,
                content: content,
                confirm: Handler.alloc(null, function (e) {
                    return resolve(e)
                }),
                completeCb: Handler.alloc(null, function (e) {
                    return resolve(e)
                }),
                moneyDesc: moneyDesc,
                money: money,
                buyNum: buyNum,
                buyMoney: buyMoney
            })
        })
    }

    public static async alertItemInfo(itemId, count = 0) {
        if (itemId > 0) {
            const item = await Item.getItemUseId(itemId)
            this.checkItem(item)
            return
        } else {
            this.checkItem(null, null, {
                moneySign: itemId,
                count: count
            })
        }
    }

    public static alertItemByData(itemData) {
        this.checkItem(itemData)
    }

    public static async mainItemInfo(itemList, title = '查看信息') {
        if (itemList.length <= 0) return
        return facade.showView(ModName.Main, MainViewType.MainItem, {
            list: itemList,
            title: title
        })
    }

    public static alertImgMessage(item, content, title = '提示', confirmCallback?, buttonList?, isClick = false, isEasyHide = true) {
        if (content || confirmCallback) {
            var cancelButton = {
                name: GameText.STR_CMD_CANCEL,
                type: -1
            }
            if (buttonList) {
                if (buttonList.length == 1) {
                    buttonList.unshift(cancelButton)
                } else {
                    buttonList.push(cancelButton)
                }
            } else {
                buttonList = [cancelButton]
                buttonList.push({
                    name: '确定',
                    type: -1
                })
            }
            facade.showView(ModName.Main, MainViewType.AlertImg, {
                title: title,
                content: content,
                confirm: confirmCallback,
                determine: buttonList,
                item: item,
                isClick: isClick,
                isEasyHide: isEasyHide
            })
        }
    }

    public static showRankReward(rewardData, title?) {
        this.showMoreRankReward([rewardData], null, title)
    }

    public static showMoreRankReward(list, tabList, title?: string) {
        this.showModal(ModName.Main, MainViewType.RankReward, { list, tabList, title })
    }

    public static hasUI() {
        return 0 != Layer.modal.numChildren || null != UIStack.getStackTop()
    }

    public static hasWindow() {
        return null != UIStack.getStackTop()
    }

    public static showMain() {
        if (BattleLogic.ins.isFight) {
            return
        }
        this.hideTop()
        UIStack.clearStack()
        if (!this.isMainShowing) {
            facade.showView(ModName.Main, MainViewType.Main)
        }
    }

    public static hideMain() {
        if (this.isMainShowing) {
            facade.hideView(ModName.Main, MainViewType.Main)
        }
    }

    public static showWin(modName: ModName, viewType: string, options?: any) {
        if (LevelOpen.ins.isShow(modName, viewType)) {
            this.hideTop()
            const isViewShown = facade.showView(modName, viewType, options)
            if (isViewShown) {
                UIStack.push(modName, viewType, options)
            }
        }
    }

    public static showBack() {
        this.hideTop(true)
        this.showTop()
    }

    public static showModal<TModName extends string, TViewType extends string>(modName: TModName, viewType: TViewType, options?: ShowViewOptions<TModName, TViewType>) {
        if (LevelOpen.ins.isShow(modName, viewType)) {
            facade.showView(modName, viewType, options)
        }
    }

    public static showTip(moduleName: string, viewType: string, options?: any) {
        facade.showView(moduleName, viewType, options)
    }

    public static hideView(moduleName: string, viewType: string) {
        facade.hideView(moduleName, viewType)
        const stackTop = UIStack.getStackTop()
        if (stackTop && stackTop.mName == moduleName && stackTop.vType == viewType) {
            UIStack.pop()
            this.showTop()
        }
    }

    public static hideTop(shouldPop = false) {
        const stackTop = UIStack.getStackTop()
        if (null != stackTop) {
            facade.hideView(stackTop.mName, stackTop.vType)
            if (shouldPop) {
                UIStack.pop()
            }
        }
    }

    public static showTop() {
        const stackTop = UIStack.getStackTop()
        if (stackTop) {
            facade.showView(stackTop.mName, stackTop.vType, stackTop.data, null, true)
            return true
        } else {
            facade.sendNt(MainEvent.UISTACK)
            return false
        }
    }

    public static closeAllUI(shouldHideMain = false) {
        UIStack.clearStack()
        Layer.hideMdr(Layer.modal)
        Layer.hideMdr(Layer.window)
        Layer.hideMdr(Layer.upperWin)
        if (shouldHideMain) {
            this.hideMain()
        }
    }

    public static createGoodsUI(t) {
        GameWorld.doGetItemSuit(GameWorld.myPlayer.getBagAllEquip())
        this.showWin(ModName.Goods, GoodsViewType.GoodsMain, {
            params: t,
            subParams: t
        })
    }

    public static createMissionUI(title: string, textInfo: string, choiceMenuList: string[], choiceObjList: number[], selectList, subType, uiObj) {
        if (!go.openPay && choiceMenuList.length > 0)
            for (var u = 0; u < choiceMenuList.length; u++) {
                var _ = choiceMenuList[u]
                _[0] && _[0].indexOf('商店') > 0 && choiceMenuList.splice(u, 1)
            }
        facade.showView(ModName.Mission, MissionViewType.MissionDialog, {
            title: title,
            textInfo: textInfo,
            choiceMenuList: choiceMenuList,
            choiceObjList: choiceObjList,
            selectList: selectList,
            subType: subType,
            uiObj: uiObj
        })
    }

    public static createPayInfoListUI() {
        console.log('createPayInfoListUI')
    }

    public static checkItemInBag(item, tabStr?: string, eventType?, fun?, isCheck = false) {
        this.showModal(ModName.Bag, BagViewType.BagItemCheck, {
            item,
            tabStr,
            eventType,
            fun,
            isCheck
        })
    }

    public static openItemAttr(t, i) {
        var o = {
            eui: t,
            item: i
        }
        this.showModal(ModName.Main, MainViewType.MainItemAttr, o)
    }

    public static openOptional(subIdx: number, subParams?) {
        this.showWin(ModName.Activity, ActivityViewType.ActivityOptional, {
            subIdx,
            subParams
        })
    }

    public static openOptonalOne() {
        var e = facade.getProxy(ModName.Activity, ProxyType.Activity)
        e.doOptionalInfoOne()
    }

    public static checkItem(item, player = null, moneyData?) {
        const n = {
            player,
            item,
            moneyData
        }
        this.showModal(ModName.Main, MainViewType.ItemCheck, n)
    }

    public static checkPet(params?, subIdx?, type?, parameter?) {
        this.showWin(ModName.Pet, PetViewType.PetInfoMain, {
            params,
            subIdx,
            subParams: {
                type,
                parameter
            }
        })
    }

    public static checkPetRaider(t, i, o, n) {
        var r = {
            params: t,
            subIdx: i,
            subParams: {
                type: o,
                parameter: n
            }
        }
        this.showWin(ModName.Raiders, RaidersViewType.PetRaiderInfo, r)
    }

    public static updateDataToEquipPopUI(t, i, o) {
        if (o) {
            this.showModal(ModName.Bag, BagViewType.ItemCompare, {
                player: t,
                item: i
            })
        }
    }

    public static updateIdentifyMessageUI(item: Item, player: PlayerVo, newItem: Item, checkRs: number, info: string, isIdentifyUpgrade: boolean) {
        const data = {
            checkRs,
            item,
            player,
            newItem,
            info,
            isIdentifyUpgrade
        }
        let viewType = checkRs != Define.BAG_CHECKUP_RESULT_UPDATE
            ? BagViewType.ItemIdentifyAlert
            : BagViewType.ItemIdentifyResult

        this.showModal(ModName.Bag, viewType, data)
    }

    public static inputItem(item: Item, inBag = false) {
        this.showModal(ModName.Bag, BagViewType.BagItemInput, {
            item: item,
            inBag: inBag
        })
    }

    public static createEnchantUI(t) {
        if (null != t && null != t.vItemList && null != t.item) {
            this.showWin(ModName.Bag, BagViewType.ItemEnChantUse, {
                enchant: t
            })
        }
    }

    public static showMissionTips(e) {
        facade.showView(ModName.Mission, MissionViewType.MissionTips, e)
    }

    public static showReward(items, title = GameText.REWARD_TITLE, handler: Handler = null, labDesc = '', money1 = 0, money2 = 0, money3 = 0, exp = 0) {
        if (this.isDelay) {
            this.delayData = Object.create(null)
            this.delayData.list = items
            this.delayData.title = title
            this.delayData.handler = handler
            this.delayData.labDesc = labDesc
            this.delayData.money1 = money1
            this.delayData.money2 = money2
            this.delayData.money3 = money3
            this.delayData.exp = exp
            return
        }
        console.log('奖励弹窗打开')
        UIHandler.showModal(ModName.Main, MainViewType.MainReward, {
            list: items,
            title: title,
            handler: handler,
            labDesc: labDesc,
            money1: money1,
            money2: money2,
            money3: money3,
            exp: exp
        })
    }

    public static delayRew() {
        if (this.delayData) {
            this.showReward(
                this.delayData.list,
                this.delayData.title,
                this.delayData.handler,
                this.delayData.labDesc,
                this.delayData.money1,
                this.delayData.money2,
                this.delayData.money3,
                this.delayData.exp
            )
            this.delayData = null
        }
    }

    public static showRewardAsk(t, i = GameText.REWARD_TITLE, o = null, n = '', r = 0, s = 0, l = 0, u = 0) {
        this.showModal(ModName.Main, MainViewType.MainRewardAsk, {
            list: t,
            title: i,
            handler: o,
            labDesc: n,
            money1: r,
            money2: s,
            money3: l,
            exp: u
        })
    }

    public static updateWorldPlayerInfoUI() {
        facade.sendNt(MainEvent.ON_UPDATE_PLAYER_INFO_UI)
    }

    public static createCityInfoUI(e?) {
        City.doViewCityInfo(null, e)
    }

    public static showInput(e, t, i, n?, r?, s?, l?, u?, _?) {
        facade.showView(ModName.Main, MainViewType.InputAlert, {
            title: e,
            tips: t,
            confirmCb: i,
            maxChars: n,
            inputHeight: r,
            cancelCb: s,
            desc: l,
            restrict: u,
            isEmpty: _
        })
    }

    public static showInputAsync(e, t, i?, o?, a?, r?, s?, l?, u?) {
        const self = this
        return new Promise<string>(function (resolve, reject) {
            self.showInput(
                e,
                t,
                Handler.alloc(null, function (e) {
                    resolve(null != i ? i.exec(e) : e)
                }),
                o,
                a,
                Handler.alloc(null, function (e) {
                    resolve(null)
                    resolve(null != r ? r.exec() : null)
                }),
                s,
                l,
                u
            )
        })
    }

    public static showInput2(e, t, i, n, r, s, l, u, _) {
        var d = ModName.Main,
            c = MainViewType.InputAlert2
        n && ((d = n[0]), (c = n[1]))
        facade.showView(d, c, {
            title: e,
            tips: t,
            confirmCb: i,
            maxChars: s,
            inputHeight: l,
            cancelCb: u,
            restricts: r,
            desc: _
        })
    }

    public static showInput2Async(e, t, i, o, a, r, s?, l?) {
        var u = this
        return new Promise(function (_, d) {
            u.showInput2(
                e,
                t,
                Handler.alloc(null, function (e) {
                    _(null != i ? i.exec(e) : e)
                }),
                o,
                a,
                r,
                s,
                Handler.alloc(null, function (e) {
                    _(null)
                }),
                l
            )
        })
    }

    public static showInputCheck(e, t, i = [], n, r, s, l) {
        facade.showView(ModName.Main, MainViewType.InputAlertCheck, {
            title: e,
            listData: t,
            listCheck: i,
            confirmCb: n,
            cancelCb: r,
            desc: s,
            selectedIndex: l
        })
    }

    public static getInputAlertDatas(titles: string[], contents = [], inputTypes = [], defaultValues = [], maxLengths = []) {
        const alertDataList = []
        for (let index = 0; index < titles.length; index++) {
            const title = titles[index]
            const content = contents ? contents[index] : null
            const inputType = inputTypes ? inputTypes[index] : null
            const defaultValue = defaultValues ? defaultValues[index] : null
            const maxLength = maxLengths ? maxLengths[index] : null
            const alertData = new InputAlertData(title, content, inputType, defaultValue, maxLength)
            alertDataList.push(alertData)
        }
        return alertDataList
    }

    public static showInputCheckAsync(e, t, i = [], o?, a?, r?) {
        var s = this
        return new Promise(function (l, u) {
            s.showInputCheck(
                e,
                t,
                i,
                Handler.alloc(null, function (e) {
                    l(null != o ? o.exec(e) : e)
                }),
                Handler.alloc(null, function (e) {
                    l(null)
                }),
                a,
                r
            )
        })
    }

    public static showOperateItem(e, t, i, n, r) {
        var s = {
            item: e,
            title: t,
            confirmCb: i,
            cancelCb: n,
            desc: r
        }
        facade.showView(ModName.Bag, BagViewType.ItemOperation, s)
    }

    public static showOperateItemAsync(e, t, i?): Promise<any> {
        const self = this
        return new Promise(function (resolve, reject) {
            self.showOperateItem(
                e,
                t,
                Handler.alloc(null, function (e) {
                    resolve(e)
                }),
                null,
                i
            )
        })
    }

    public static createShopUI(t) {
        if (go.openPay) {
            this.SUB_SHOP_TITLLE_INDEX = t
            if (t == this.SUB_SHOP_COMBIN || t == UIDefine.SUB_SHOP_ENCHANT || t == UIDefine.SUB_SHOP_EQ_UPGRADE) {
                console.log('附魔商店、合成商店、进阶商店')
                return GameWorld.doEnterCombinShop()
            } else {
                if (t == this.SUB_SHOP_INTEGRAL || t == this.SUB_SHOP_ARENA || t == this.SUB_SHOP_SKYARENA || t == this.SUB_SHOP_TOWER || t == this.SUB_SHOP_CAMPWAR) {
                    console.log('积分商店、天空竞技场商店、竞技场商店、永泽塔商店、阵营战商店')
                    return GameWorld.doGetIntegralShopList()
                } else {
                    if (t == this.SUB_SHOP_PETCOMPOSITE) {
                        PetComposite.doGetPetCompositeShopData()
                    }
                }
            }
        }
        return null
    }

    /**
     * 弹出出售物品确认弹窗
     * @param list 物品列表
     * @param title 
     */
    public static showSellItems(list: Vector<Item>, title = '出售') {
        this.showModal(ModName.Shop, ShopViewType.ShopAutoSell, {
            vItemLists: list,
            title: title
        })
    }

    public static showPrivateChat(t) {
        var i = facade.getProxy(ModName.Chat, ProxyType.Chat)
        i.data.PChatTarget = t
        this.showWin(ModName.Chat, ChatViewType.ChatAllView, {
            channel: Define.CHAT_TYPE_PRIVATE
        })
    }

    public static createGeneralTeamUI(t, i) {
        this.showWin(ModName.Team, TeamViewType.GeneralTeam, {
            list: t,
            title: i
        })
    }

    public static createOnLineRewardUI(subIdx: number, subParams?) {
        this.showWin(ModName.Activity, ActivityViewType.WelfareMain, {
            subIdx,
            subParams
        })
    }

    public static createActivityOpenUI(subIdx: number, subParams?) {
        this.showWin(ModName.Activity, ActivityViewType.ActivityOpen, {
            subIdx,
            subParams
        })
    }

    static set isSkyArenaClose(value) {
        this._isSkyArenaClose = value
    }

    public static openSkyArenaproxy() {
        facade.getProxy(ModName.SkyArena, ProxyType.SkyArena).openSkyArena(null, true)
    }

    public static battleOpenModule(e: number) {
        this.skyArenaType = e
        if (null != GameWorld.teamBoss) {
            GameWorld.teamBoss.setStatus(!(2 == this.skyArenaType), TeamBoss.STATUS_FIGHT_FAIL)
            GameWorld.teamBoss.setStatus(true, TeamBoss.STATUS_FIGHT_EXIT)
            2 != this.skyArenaType && (GameWorld.teamBoss = null)
        }
    }

    public static openChat() {
        this.showWin(ModName.Chat, ChatViewType.ChatAllView)
    }

    public static openBag(plr = GameWorld.myPlayer, subIdx = 0) {
        const bagProxy = facade.getProxy(ModName.Bag, ProxyType.Bag)
        bagProxy.data.PlayerVo = plr
        const petProxy = facade.getProxy(ModName.Pet, ProxyType.Pet)
        petProxy.data.PlayerVo = plr
        this.showWin(ModName.Bag, BagViewType.BagMain, {
            subIdx: subIdx
        })
    }

    public static openPlayerShop() {
        this.createWorldEquipUI(GameWorld.myPlayer, 1)
    }

    public static openPet(t = GameWorld.myPlayer, i = 0) {
        var n = facade.getProxy(ModName.Pet, ProxyType.Pet)
        n.data.PlayerVo = t
        this.showWin(ModName.Pet, PetViewType.PetMain, {
            subIdx: i
        })
    }

    public static openPetInfo() {
        var e = facade.getProxy(ModName.Pet, ProxyType.Pet)
        e.openBattlePet()
    }

    public static openPetUpGrade(e) {
        var t = facade.getProxy(ModName.Bag, ProxyType.Bag)
        t.openPetEquipUp(e)
    }

    public static openMail() {
        this.showWin(ModName.Mail, MailViewType.Mail)
    }

    public static openRole(t = GameWorld.myPlayer, i = 0) {
        var n = facade.getProxy(ModName.Role, ProxyType.Role)
        n.data.PlayerVo = t
        this.showWin(ModName.Role, RoleViewType.RoleMain, {
            subIdx: i
        })
    }

    public static openRoleTalenLearn(t) {
        this.showModal(ModName.Role, RoleViewType.RoleTalenLearn, t)
    }

    public static openMainInfo(content: any, count?: number, callback?: any) {
        let contentList = []
        if (typeof count === 'number') {
            for (let index = 0; index < count; index++) {
                contentList.push(content + '_' + count)
            }
        } else if (typeof content === 'number') {
            const tutorialData = TutorialData[content]
            contentList = tutorialData ? tutorialData.atlas : []
        } else {
            contentList = content
        }

        if (contentList.length <= 0) {
            console.log('切图被拦截===option:', content, '===num:', count, '===fun:', callback)
            return
        }

        const options = {
            list: contentList,
            fun: callback
        }

        this.showModal(ModName.Main, MainViewType.MainInfo, options)
    }

    public static openMainStory() {
        GameWorld.myPlayer && this.showTip(ModName.Main, MainViewType.MainStory)
    }

    public static openMainLevelAnim(t) {
        facade.sendNt(MainEvent.MAIN_LV_LIN_CLOSE)
        t.length <= 0 ||
            this.showTip(ModName.Main, MainViewType.MainLevelAnim, {
                arr: t
            })
    }

    public static unifiedOpen(t) {
        switch (t) {
            case 0:
                this.openChat()
                break
            case 1:
                this.openBag()
                break
            case 2:
                this.openPet()
                break
            case 3:
                this.openRole()
                break
            case 4:
                this.openMail()
        }
    }

    public static createAchieveManageUI() {
        this.showWin(ModName.Achievement, AchievementType.Achievement)
    }

    public static cerateItemDesc(t, i) {
        switch (t) {
            case Define.MONEY1:
            case Define.MONEY2:
            case Define.MONEY3:
            case Define.EXP:
            case Define.ACTIVE:
            case Define.SCORE_TOWER:
            case Define.TYPE_ENERGY_ESSENCE:
            case Define.TYPE_DEFEND_SCORE:
            case Define.TYPE_CAMPWAR_SCORE:
                var o = ModelVo.getMoneyText(t) + 'x' + i
                this.alertMessage(o)
                return false
            default:
                var n = SpecialIconUtil.getSpecialIconData(t)
                if (n) {
                    var a = n.name + 'x' + i
                    this.alertMessage(a)
                    return false
                }
                return true
        }
    }

    public static createRankListUI() {
        this.showWin(ModName.Leaderboard, LeaderboardType.Leaderboard)
    }

    public static createMasterListUI() {
        this.showWin(ModName.Relation, RelationViewType.RelationMaster)
    }

    public static createBattleRecord(t?) {
        this.showWin(ModName.Battle, BattleViewType.BattleRecord, t)
    }

    public static createChargeUI(subIdx, subParams?) {
        if (go.openPay) {
            facade.sendNt(UserVip.USERVIP_CLOSE)
            this.showWin(ModName.Charge, ChargeType.ChargeMain, {
                subIdx: subIdx,
                subParams: subParams
            })
        }
    }

    public static createFurnaceUI() {
        this.showWin(ModName.Furnace, FurnaceViewType.FurnaceMain)
    }

    public static createFurNaceItemListUI(list, title?) {
        this.showModal(ModName.Furnace, FurnaceViewType.FurnaceList, {
            list,
            title
        })
    }

    public static async createEscortRobListUI() {
        const data = await Escort.doEscortRobListMsg()
        if (data.length <= 0) {
            return this.alertMessage(GameText.STR_ESCORT_ROB_LIST_NULL)
        }
        this.showWin(ModName.Escort, EscortViewType.EscortRobberList, data.arr)
    }

    public static async createNewEscortRobListUI() {
        const data = await NewEscort.doEscortRobListMsg()
        if (data.length <= 0) {
            return this.alertMessage(GameText.STR_ESCORT_ROB_LIST_NULL)
        }
        this.showWin(ModName.Escort, EscortViewType.EscortRobberList, data.arr)
    }

    public static processEscrotRefreshPosMsg(e, t) {
        NewEscort.processEscrotRefreshPosMsg(e, t)
    }

    public static processEscrotNewIoncPosMsg(e, t) {
        var i = e.getByte()
        if (i > 0)
            for (var n = 0; n < t.pointList.length; n++) {
                var a = t.pointList[n]
                a.icon = e.getByte()
            }
        facade.sendNt(EscortEvent.ESCORT_EVENT_LISTUP)
    }

    public static createActivityShareUI() {
        this.showWin(ModName.Activity, ActivityViewType.ActivityShare)
    }

    public static createPetAttributeInfoUI(e) {
        facade.showView(ModName.Pet, PetViewType.PetAttributeInfo, e)
    }

    public static createAchieveDescUI(t, i = '提示', o?, n?, a = false) {
        var r = new StringBuffer()
        r.append(Utilities.manageString(GameText.ACHIVENAME, t.name))
        r.append(Utilities.manageString('\n' + GameText.ACHIVEDESC, t.desc))
        if ('' != t.title) {
            r.append(Utilities.manageString('\n' + GameText.ACHIVENINCKNAME, Tool.composeString([[t.title, Tool.COLOR_GREEN]], -1, 2, 24)))
        } else {
            r.append(' \n')
        }
        if (t.money1 > 0) {
            r.append(PowerString.makeColorString(GameText.getMoneyText(proto.ModelConst.Type.MONEY1) + '：' + t.money1 + ' ', Tool.COLOR_YELLOW))
        }
        if (t.money2 > 0) {
            r.append(PowerString.makeColorString(GameText.getMoneyText(proto.ModelConst.Type.MONEY2) + '：' + t.money2 + ' ', Tool.COLOR_YELLOW))
        }
        if (t.money3 > 0) {
            r.append(PowerString.makeColorString(GameText.getMoneyText(proto.ModelConst.Type.MONEY3) + '：' + t.money3 + ' ', Tool.COLOR_YELLOW))
        }
        if (a) {
            r.append('\n卍')
        }
        this.alertImgMessage(t.item, r.toString(), i, o, n)
    }

    public static async createPetBattleEditUI(subIdx: number) {
        await this.showWin(ModName.Pet, PetViewType.PetBattleEditMain, { subIdx })
    }

    public static async createPetBattlePKUI(subIdx: number) {
        await this.showWin(ModName.Pet, PetViewType.PetBattlePKMain, { subIdx })
    }

    public static async createSeeAchieveUI(t, i) {
        const o = new Achieve()
        o.setId(t.getId())
        if (i) {
            o.setPlayType(Achieve.PLAYER_TYPE_SHOP)
        } else {
            o.setPlayType(Achieve.PLAYER_TYPE_NORMAL)
        }
        const n = await Achieve.doSeeAchieveListMsg(o)
        if (n) {
            this.showWin(ModName.Role, RoleViewType.RoleAchieve, {
                model: t,
                achieve: o
            })
        }
    }

    public static createCostRewardUI() {
        this.showWin(ModName.Charge, ChargeType.ChargeCostReward)
    }

    public static async createLotteryDrawUI(t) {
        const i = facade.getProxy(ModName.Charge, ProxyType.Charge)
        const data = await i.doActorLotteryDrawList(this.EVENT_LOTTERY_DRAW_LIST_TAB_NOW)

        if (data) {
            this.EVENT_LOTTERY_DRAW_LIST_TAB_NOW == t
                ? this.showWin(ModName.Charge, ChargeType.ChargeLotteryMain, {
                    subIdx: 0
                })
                : this.EVENT_LOTTERY_DRAW_LIST_TAB_HISTORY == t &&
                this.showWin(ModName.Charge, ChargeType.ChargeLotteryMain, {
                    subIdx: 1
                })
        }
    }

    public static createDigHoleUI() {
        if (GameWorld.myPlayer.isShopMode()) {
            return this.alertMessage('正在摆摊，无法进入矿洞')
        }
        this.showWin(ModName.DigHole, DigHoleViewType.DigHole)
    }

    public static createRoleRevelation() {
        this.showWin(ModName.Role, RoleViewType.RoleRevelation)
    }

    public static createActivitInfoUI(subIdx: number, subParams?) {
        this.showWin(ModName.Activity, ActivityViewType.ActivityMain, {
            subIdx,
            subParams,
            hashCode: Math.random()
        })
    }

    public static createTeamUI() {
        var t = GameWorld.getTypeModel(Define.NEAR_ALL)
        if (0 == t.arr.length) {
            this.alertMessage(GameText.STR_NEAR_NO_PLAYER)
            return
        }
        facade.sendNt(MainEvent.CLOSE_ALERT_VIEW)
        this.showWin(ModName.Team, TeamViewType.TeamMain)
    }

    public static createMissionMainUI(subIdx: number, subParams?) {
        this.showWin(ModName.Mission, MissionViewType.MissionMain, {
            subIdx,
            subParams
        })
    }

    public static createNewMission() {
        this.showWin(ModName.Mission, MissionViewType.NewMission)
    }

    public static createCrossPla() {
        this.showWin(ModName.Arena, ArenaViewType.CrossPlaMain)
    }

    public static createProMission() {
        this.showWin(ModName.Mission, MissionViewType.PushengMission)
    }

    public static createDailyMission(t = 0, i?) {
        this.showWin(ModName.Mission, MissionViewType.MissionDailyMain, {
            subIdx: t,
            subParams: i
        })
    }

    public static createPetBattle() {
        if (GameWorld.myPlayer.isPlayerTeam()) {
            return this.alertMessage('宠物对战暂时只支持单人对战，组队状态无法进入')
        }
        return this.showWin(ModName.Pet, PetViewType.PetBattleMain)
    }

    public static setUIIntoType(e) {
        this.roleUserType = e
    }

    public static createSeePlayerUI() {
        this.showWin(ModName.Role, RoleViewType.RoleUserMain)
    }

    public static createMercenaryInfoUI(t) {
        this.showWin(ModName.Pet, PetViewType.PetMerInfoMain)
    }

    public static createSigninUI() {
        ActivitySignIn.signInInfo()
    }

    public static createPetRaidersUI() {
        this.showWin(ModName.Raiders, RaidersViewType.RaidersMain, {
            subIdx: 0
        })
    }

    public static createMountRaidersUI() {
        this.showWin(ModName.Raiders, RaidersViewType.RaidersMain, {
            subIdx: 1
        })
    }

    public static createMonsterListUI() {
        this.showWin(ModName.Raiders, RaidersViewType.RaidersMain, {
            subIdx: 2
        })
    }

    public static createItemRaidersUI() {
        this.showWin(ModName.Raiders, RaidersViewType.BookGroupMain)
    }

    public static createChiselSealUI() {
        this.showWin(ModName.ChiselSeal, ChiselSealViewType.ChiselSealMain)
    }

    public static createChoiceMenu(e, t?, i?, n?) {
        facade.showView(ModName.Relation, RelationViewType.RelationMenu, {
            fun: i,
            list: e instanceof Vector ? e.arr : e,
            title: t,
            alpha: n
        })
    }

    public static createAreaMessageWin(e, t, i, n?, r?) {
        facade.showView(ModName.Main, MainViewType.AreaMessage, {
            fun: i,
            list: t instanceof Vector ? t.arr : t,
            desc: e,
            title: n,
            textAlign: r
        })
    }

    public static doPetSee(e, t) {
        if (null != e && e.isPetType()) {
            var i = new MyPetVo(null)
            i.petItem = e
            MyPetVo.doPetInfoMsg(i, MsgHandler.PET_INFO_CHAT, [t, e.slotPos])
        }
    }

    public static async createActorBuyListUI() {
        this.showWin(ModName.Shop, ShopViewType.ShopRoleBuy)
    }

    public static isShowResolveMenu() {
        return true
    }

    public static async createWorldEquipUI(t, i) {
        if (!t) {
            return
        }
        if (!t.bag) {
            this.errorMessage(GameText.STR_BAG_NULL)
            return
        }
        if (i === 2 && this.isShowResolveMenu()) {
            return
        }
        if (i === 3 && Enchant.isOpen()) {
            const n = t !== GameWorld.myPlayer
            facade.sendNt(MainEvent.CLOSE_ALERT_VIEW)
            await GameWorld.doGetItemSuit(t.getBagAllEquip())
            this.openBag(t, i)
            return
        }
        if (i === 2) {
            this.errorMessage('20级后开放附魔功能')
            return
        }
        if (i === 3) {
            this.errorMessage('20级后开放分解功能')
            return
        }
    }

    public static async createStorageUI(t, i) {
        if (!i || !i.bag) {
            return null
        }
        if (t !== this.SUB_STORAGE_PLAYER) {
            if (t === this.SUB_STORAGE_OTHER_PLAYER) {
                // ...
            } else if (t === this.SUB_STORAGE_COUNTRY) {
                // ...
            } else if (t === this.SUB_STORAGE_COUNTRY_SYSTEM) {
                // ...
            } else if (t === this.SUB_STORAGE_PLAYER_VIP) {
                const o = await GameWorld.doVIPStorageListAction(i)
                if (!o) {
                    console.log(Tool.manageString(GameText.STR_OPEN_VIP_STORAGE_FAIL, 'VIP'))
                    return false
                }
            } else if (t === UIDefine.SUB_STORAGE_PLAYER_HIGHT_VIP) {
                const o = await GameWorld.doHightVIPStorageListAction(i)
                if (!o) {
                    console.log(Tool.manageString(GameText.STR_OPEN_VIP_STORAGE_FAIL, GameText2.STR_HIGHT_VIP_STORAGE))
                    return false
                }
            } else if (t === this.SUB_STORAGE_OTHER_PLAYER_VIP) {
                // ...
            }
        }
        await GameWorld.doGetItemSuit(i.getBagAllEquip())
        this.showWin(ModName.Bag, BagViewType.BagStorage, {
            player: i,
            type: t
        })
    }

    public static createCountryUI(t) {
        this.showWin(
            ModName.Country,
            CountryViewType.CountryInfoMain,
            t
                ? {
                    subParams: {
                        data: t
                    }
                }
                : null
        )
    }

    public static createActivityListUI(t) {
        if (t == this.SUB_ACTIVITY_LIST) {
            // no code
        } else {
            if (t != this.SUB_INFO_DATA_LIST) return
            this.showWin(ModName.Leaderboard, LeaderboardType.GameHelpView)
        }
    }

    public static openCrossNew(e = false) {
    }

    public static addSystemMenu(t: Vector<string>, i: Vector<ChoiceMenuItem>) {
        null != GameWorld.myPlayer &&
            GameWorld.myPlayer.get(proto.ModelConst.Type.LEVEL) >= 10 &&
            go.openPay &&
            Tool.addChoiceMenu(t, GameText.STR_GAME_INFODATA_LIST, i, this.EVENT_ALL_MENU_INFODATA_LIST)
        Tool.addChoiceMenu(t, GameText.STR_REFRESH_MENU, i, this.EVENT_ALL_MENU_SYSTEM_REFLASH)
        Tool.addChoiceMenu(t, GameText.STR_REQUEST_LIST_MENU, i, this.EVENT_ALL_MENU_PLAYER_EVENT)
        Tool.addChoiceMenu(t, GameText.STR_ROLE_LIST, i, this.EVENT_ALL_MENU_SYSTEM_PLAYER_LIST)
        Tool.addChoiceMenu(t, GameText.STR_SYSTEM_MOVE, i, this.EVENT_ALL_MENU_SYSTEM_MOVE)
        Tool.addChoiceMenu(t, '返回登录', i, this.EVENT_ALL_MENU_SYSTEM_QUIT)
        Tool.addChoiceMenu(t, '账号注销', i, this.EVENT_DEL_ACCOUNT)
        Tool.addChoiceMenu(t, '激活码', i, UIDefine.EVENT_ALL_DUIHUAN_SHOP)
        Tool.addChoiceMenu(t, '用户中心', i, this.EVENT_OPEN_USER_CENTER)
        Tool.addChoiceMenu(t, '用户协议', i, this.EVENT_OPEN_USER_AGREEMENT)
        Tool.addChoiceMenu(t, '隐私政策', i, this.EVENT_OPEN_USER_POLICY)
        Tool.addChoiceMenu(t, '账号绑定', i, this.EVENT_ACCOUNT_BIND)
        Tool.addChoiceMenu(t, '联系客服', i, this.EVENT_OPEN_CUSTOMER_SERVICE)
        Tool.addChoiceMenu(t, '解绑个人信息', i, this.EVENT_CHANGE_BIND)
    }

    public static createPlayerEventUI() {
        GameWorld.refreshPlayerEvent() || this.showWin(ModName.System, SystemViewType.SystemEventView)
    }

    public static getSettingText(t, i) {
        return this.SETTING_TEXT[t - this.EVENT_SETTING_SET_NAME_SIMPLE][i]
    }

    public static findSettingIndex(t) {
        for (var i = 0; i < this.SETTING_MENU.length; i++) for (var o = 0; o < this.SETTING_MENU[i].length; o++) if (this.SETTING_MENU[i][o] == t) return i
        return -1
    }

    public static isMultiple(t) {
        return 2 == this.SETTING_MENU[t].length && (0 == this.SETTING_MENU[t][1] || 1 == this.SETTING_MENU[t][1])
    }

    public static setSettingIndex(t, i) {
        var o = this.findSettingIndex(i)
        if (0 > o) return t
        var n = this.EVENT_ALL_SETTING_INVITE_REJECT,
            a = 1 << (i - n)
        if (this.isMultiple(o)) t = Tool.setBit(!Tool.isBit(a, t), a, t)
        else {
            for (var r = 0; r < this.SETTING_MENU[o].length; r++) this.SETTING_MENU[o][r] >= n && (t = Tool.setBit(false, 1 << (this.SETTING_MENU[o][r] - n), t))
            i >= n && (t = Tool.setBit(true, a, t))
        }
        return t
    }

    public static getSettingIndex(t, i) {
        var o = this.findSettingIndex(i)
        if (0 > o) return false
        var n = this.EVENT_ALL_SETTING_INVITE_REJECT
        if (this.isMultiple(o)) {
            var a = Tool.isBit(1 << (i - n), t)
            return 0 == this.SETTING_MENU[o][1] ? !a : a
        }
        if (!(n > i)) return Tool.isBit(1 << (i - n), t)
        for (var r = 0; r < this.SETTING_MENU[o].length; r++) if (this.SETTING_MENU[o][r] >= n && Tool.isBit(1 << (this.SETTING_MENU[o][r] - n), t)) return false
        return true
    }

    public static showCheckAlert(e, t) {
        return new Promise(function (i, r) {
            var s = Handler.alloc(null, function (e) {
                i(e)
            })
            facade.showView(ModName.Main, MainViewType.CheckAlert, {
                list: e,
                title: t,
                confirmCb: s
            })
        })
    }

    public static showCheckHAlert(e, t, i, r) {
        return new Promise(function (s, l) {
            var u = Handler.alloc(null, function (e, t) {
                s([e, t])
            })
            facade.showView(ModName.Main, MainViewType.CheckHAlert, {
                list: e,
                listH: t,
                desc: i,
                title: r,
                confirmCb: u
            })
        })
    }

    public static FRAME_BORDER = 7
    public static EVENT_DEBUG_WORLD_WIDNOW = 9999
    public static EVENT_DEBUG_LIST_WINDOW = 9998
    public static EVENT_MISSION_FIGHT = 1e3
    public static EVENT_NOTE = 910
    public static EVENT_ALL_INFO_SKIP_ANIME = 932
    public static EVENT_ALL_INFO_LEADER_FORCE = 933
    public static EVENT_ALL_INFO_PET_ORDER_AUTO = 934
    public static EVENT_ALL_INFO_NEW_MAIL = 936
    public static EVENT_ALL_INFO_DUR_BROCKEN = 937
    public static EVENT_ALL_INFO_TEAM_ICON = 938
    public static EVENT_ALL_INFO_PET_ORDER_HAND = 940
    public static EVENT_ALL_INFO_NEW_EVENT = 941
    public static EVENT_ALL_INFO_NEW_PRIVATE_CHAT = 942
    public static EVENT_ALL_INFO_PLAYER_INFO_HPMP = 945
    public static EVENT_ALL_INFO_PET_INFO_HEAD = 946
    public static EVENT_ALL_INFO_PET_INFO_HPMP = 947
    public static EVENT_ALL_QUIT_BATTLE_SEE = 959
    public static EVENT_ALL_AUTOMODEL_ESCAPE = 967
    public static EVENT_ALL_BACK = -1
    public static EVENT_ALL_MENU_CHAT = 11e3
    public static EVENT_ALL_MENU_FRIEND = 11001
    public static EVENT_ALL_MENU_BLACK = 11002
    public static EVENT_ALL_MENU_PK = 11003
    public static EVENT_ALL_MENU_INVITE_COUNTRY = 11004
    public static EVENT_ALL_MENU_SEE_CITY = 11005
    public static EVENT_ALL_MENU_SEND_MAIL = 11006
    public static EVENT_ALL_MENU_SEE_SHOP = 11007
    public static EVENT_ALL_MENU_INVITE_TEAM = 11008
    public static EVENT_ALL_MENU_JOIN_TEAM = 11009
    public static EVENT_ALL_MENU_SEE_INFO = 11010
    public static EVENT_ALL_MENU_DEL_RELATION = 11011
    public static EVENT_ALL_DEL_TEAM = 11012
    public static EVENT_ALL_LEAVE_TEAM = 11013
    public static EVENT_ALL_KICK_TEAMER = 11014
    public static EVENT_ALL_CHANGE_LEADER = 11015
    public static EVENT_ALL_ENTER_COUNTRY = 11016
    public static EVENT_ALL_LEAVE_COUNTRY = 11018
    public static EVENT_ALL_TASK_ACCEPT = 11023
    public static EVENT_ALL_TASK_PATH = 11024
    public static EVENT_ALL_TASK_DEL = 11025
    public static EVENT_ALL_TASK_SUBMIT = 11026
    public static EVENT_ALL_TASK_ITEM_SEE = 11027
    public static EVENT_ALL_MAIL_REPLY = 11080
    public static EVENT_ALL_MAIL_REFUSE = 11081
    public static EVENT_ALL_MAIL_REGAIN = 11082
    public static EVENT_ALL_MAIL_PICK = 11083
    public static EVENT_ALL_MAIL_DEL = 11084
    public static EVENT_ALL_MAIL_SEND = 11085
    public static EVENT_ALL_RANDOM_MISSION_REFLASH = 11030
    public static EVENT_ALL_TASK_OFF_ACTIVATE = 11094
    public static EVENT_ALL_TASK_DIRECT_STEP2 = 11095
    public static EVENT_ALL_ITEM_EQUIP = 11096
    public static EVENT_ALL_ITEM_BIND = 11097
    public static EVENT_ALL_ITEM_COMPARE = 11098
    public static EVENT_ALL_ITEM_IDENTIFY = 11099
    public static EVENT_ALL_ITEM_INLAY = 11100
    public static EVENT_ALL_ITEM_ALL_DEL = 11102
    public static EVENT_ALL_ITEM_UN_EQUIP = 11103
    public static EVENT_ALL_ITEM_USE = 11104
    public static EVENT_ALL_ITEM_DETAIL = 11105
    public static EVENT_ALL_ITEM_SCENE = 11106
    public static EVENT_ALL_ITEM_UP_SHOP = 11107
    public static EVENT_ALL_ITEM_DOWN_SHOP = 11108
    public static EVENT_ALL_ITEM_CHANGE_PRICE = 11109
    public static EVENT_ALL_ITEM_UN_COMPARE = 11110
    public static EVENT_ALL_ITEM_PUT_STORAGE = 11111
    public static EVENT_ALL_ITEM_GET_STORAGE = 11112
    public static EVENT_ALL_ITEM_PUT_COUNTRY_STORE = 11113
    public static EVENT_ALL_ITEM_GET_COUNTRY_STORE = 11114
    public static EVENT_ALL_ITEM_DEL_COUNTRY_STORE = 11115
    public static EVENT_ALL_ITEM_REFURSE = 11116
    public static EVENT_ALL_ITEM_INTEGRAL_CANCEL = 11180
    public static EVENT_ALL_ITEM_INTEGRAL_SURE = 11181
    public static EVENT_ALL_ITEM_INTEGRAL_EQUIP = 11182
    public static EVENT_ALL_ITEM_COMBIN_DO = 11184
    public static EVENT_ALL_ITEM_COMBIN_CONTINUE = 11185
    public static EVENT_ALL_ITEM_COMBIN_REPLACE = 11186
    public static EVENT_ALL_ITEM_COMBIN_REPLACE_POWER = 11188
    public static EVENT_ALL_ITEM_COMBIN_REPLACE_SKILL = 11189
    public static EVENT_ALL_ITEM_STAR = 11190
    public static EVENT_ALL_ITEM_VIP_SELL = 11191
    public static EVENT_ALL_ITEM_PUT_VIP_STORAGE = 11192
    public static EVENT_ALL_ITEM_GET_VIP_STORAGE = 11193
    public static EVENT_ALL_ITEM_REFRESH_VIP_STORAGE = 11194
    public static EVENT_ALL_ITEM_USE_ONE = 11196
    public static EVENT_BAG_CHANGE = 11197
    public static EVENT_COMMON_STAR = 12001
    public static EVENT_UPGRADE_STAR = 12002
    public static EVENT_COMMON_IDENTIFY = 12003
    public static EVENT_UPGRADE_IDENTIFY = 12004
    public static EVENT_ATTACH_TYPE_ATTCH = 12005
    public static EVENT_ATTACH_TYPE_CHANGE = 12006
    public static EVENT_ALL_ITEM_EQUIP_BY_BEST = 12007
    public static EVENT_PET_BATTLE = 12008
    public static EVENT_PET_REST = 12009
    public static EVENT_PET_VIEW = 12010
    public static EVENT_PET_LEVEL = 12011
    public static EVENT_ALL_ITEM_UN_PET_EQUIP = 12012
    public static EVENT_ALL_ITEM_PET_EQUIP = 12013
    public static EVENT_ALL_MER_PET_ITEM_RESET2 = 11137
    public static EVENT_ALL_MER_PET_ITEM_RESET = 11138
    public static EVENT_ALL_MER_PET_ADD_SKILL = 11139
    public static EVENT_ALL_MENU_COUNTRY_ADJUST_JOB = 11161
    public static EVENT_ALL_MENU_COUNTRY_BECOME_KING = 11162
    public static EVENT_SETTING_SET_NAME_SIMPLE = 10030
    public static EVENT_SETTING_SET_JOIN = 10033
    public static EVENT_SETTING_SET_INVITE = 10034
    public static EVENT_SETTING_SET_CHAT = 10035
    public static EVENT_SETTING_MASTER_ACCEPT = 10036
    public static EVENT_ALL_SETTING_INVITE_REJECT = 10050
    public static EVENT_ALL_SETTING_JOIN_ACCEPT = 10051
    public static EVENT_ALL_SETTING_JOIN_REJECT = 10052
    public static EVENT_ALL_SETTING_CHAT_WORLD_OFF = 10053
    public static EVENT_ALL_SETTING_CHAT_MAP_OFF = 10054
    public static EVENT_ALL_SETTING_CHAT_COUNTRY_OFF = 10055
    public static EVENT_ALL_SETTING_CHAT_TEAM_OFF = 10056
    public static EVENT_ALL_SETTING_CHAT_PRIVATE_OFF = 10057
    public static EVENT_ALL_SETTING_PET_PLAN_OFF = 10060
    public static EVENT_ALL_SETTING_SHOW_CHAT_OFF = 10063
    public static EVENT_ALL_SETTING_MASTER_REJECT = 10064
    public static EVENT_ALL_SETTING_MUSIC_BACKGROUND_OFF = 10065
    public static EVENT_ALL_SETTING_MUSIC_EFFECT_OFF = 10078
    public static EVENT_ALL_MENU_WORLD_GRID = 11200
    public static EVENT_ALL_MENU_WORLD_ITEM = 11201
    public static EVENT_ALL_MENU_WORLD_SETTING = 11202
    public static EVENT_ALL_MENU_WORLD_PET = 11203
    public static EVENT_ALL_MENU_WORLD_MAIL = 11204
    public static EVENT_ALL_MENU_WORLD_CHAT_ROOM = 11205
    public static EVENT_ALL_MENU_WORLD_CHAT_INPUT = 11206
    public static EVENT_ALL_MENU_WORLD_EQUIP = 11208
    public static EVENT_ALL_MENU_WORLD_PLAYER = 11209
    public static EVENT_ALL_MENU_WORLD_COUNTRY = 11211
    public static EVENT_ALL_MENU_WORLD_CITY = 11212
    public static EVENT_ALL_MY_COUNTRY = 11214
    public static EVENT_ALL_COUNTRY_WAR = 11215
    public static EVENT_ALL_MENU_ACTIVITY_LIST = 11216
    public static EVENT_ALL_MENU_INFODATA_LIST = 11217
    public static EVENT_ALL_MENU_MONSTER_LIST = 11218
    public static EVENT_ALL_MENU_RELATION = 11219
    public static EVENT_ALL_MENU_ACHIEVE = 11220
    public static EVENT_ALL_MENU_MY_CARD = 11223
    public static EVENT_ALL_MENU_SHOPING = 11224
    public static EVENT_ALL_MENU_NEAR = 11225
    public static EVENT_ALL_MENU_TEAM = 11226
    public static EVENT_ALL_MENU_NOTICE = 11227
    public static EVENT_ALL_MENU_SYSTEM_PLAYER_LIST = 11228
    public static EVENT_ALL_MENU_SYSTEM_CHAT_SET = 11229
    public static EVENT_ALL_MENU_SYSTEM_MOVE = 11230
    public static EVENT_ALL_MENU_SYSTEM_QUIT = 11231
    public static EVENT_ALL_MENU_SYSTEM_REFLASH = 11232
    public static EVENT_ALL_MENU_STALL_START = 11234
    public static EVENT_ALL_MENU_STALL_END = 11235
    public static EVENT_ALL_MENU_STALL_RECORD = 11236
    public static EVENT_ALL_MENU_SKILL_TAB = 11237
    public static EVENT_ALL_MENU_CHAT_SAME_CHANNEL = 11241
    public static EVENT_ALL_MENU_CHAT_SET = 11242
    public static EVENT_ALL_MENU_COUNTRY_HONOR = 11243
    public static EVENT_ALL_MENU_PAY_INFO = 11244
    public static EVENT_ALL_MENU_ENTER_CAMERA = 11245
    public static EVENT_ALL_MENU_SEND_MAIL_BY_ID = 11246
    public static EVENT_ALL_MENU_GAME_CLOSE = 11247
    public static EVENT_ALL_MENU_RELATION_FLY = 11248
    public static EVENT_ALL_MENU_PLAYER_EVENT = 11249
    public static EVENT_ALL_MENU_CHAT_ITEM_SEE = 11250
    public static EVENT_ALL_MENU_CHAT_MISSION_SEE = 11251
    public static EVENT_ALL_MENU_ADD_MASTER = 11252
    public static EVENT_ALL_MENU_DEL_MASTER = 11253
    public static EVENT_ALL_MENU_MODIFY_ACTOR = 11254
    public static EVENT_ALL_MENU_MODIFY_PLAYER = 11255
    public static EVENT_ALL_MENU_PLAYER_SETTING = 11256
    public static EVENT_ALL_MENU_BIND_PHONE = 11258
    public static EVENT_ALL_MENU_USER_MANAGE = 11259
    public static EVENT_ALL_MENU_GAME_SETTING = 11260
    public static EVENT_ALL_MENU_GAME_HELP = 11261
    public static EVENT_ALL_MENU_VERSION_UPDATE = 11262
    public static EVENT_ALL_MENU_RESET_PASSWORD = 11264
    public static EVENT_ALL_MENU_FIND_PASSWORD = 11265
    public static EVENT_ALL_MENU_CANCEL_BIND = 11266
    public static EVENT_ALL_MENU_CHANGE_PASSWORD = 11270
    public static EVENT_ALL_MENU_VIEW_BATTLE = 11280
    public static EVENT_ALL_MENU_WAR_RANKLIST = 11282
    public static EVENT_ALL_MENU_WAR_DECLARE = 11283
    public static EVENT_ALL_VIEW_WORLD_BUFF = 11284
    public static EVENT_ALL_MENU_CHAT_COUNTRY_SEE = 11286
    public static EVENT_ALL_MENU_RANK = 11287
    public static EVENT_ALL_MENU_SAFELOCK = 11288
    public static EVENT_ACTOR_BUY_PLAYER = 11289
    public static EVENT_ALL_MENU_SEE_ACHIEVE = 11290
    public static EVENT_ALL_MENU_SEE_ACHIEVE_SHOP = 11291
    public static EVENT_ALL_MENU_SHOPPLAYER_INFO = 11292
    public static EVENT_ALL_MENU_SHOPPLAYER_BAG = 11293
    public static EVENT_ALL_MENU_SHOPPLAYER_PETLIST = 11294
    public static EVENT_ALL_MENU_SHOPPLAYER_STORE = 11295
    public static EVENT_ALL_MENU_SHOPPLAYER_CITY = 11296
    public static EVENT_ALL_MENU_SHOPPLAYER_COUNTRY = 11297
    public static EVENT_ALL_MENU_RECMAIL_REPORT = 11298
    public static EVENT_ALL_MENU_CANTACT_SELLER = 11299
    public static EVENT_ALL_MENU_BIND_MAIL = 11301
    public static EVENT_ALL_MENU_VIP_ACTIVATE = 11302
    public static EVENT_ALL_MENU_VIP_PRIVILEGE = 11303
    public static EVENT_ALL_MENU_MASTER_LIST = 11305
    public static EVENT_ALL_MENU_LOTTERY_DRAW = 11306
    public static EVENT_ALL_MENU_MIX_SERVER_ACTOR_JOIN = 11309
    public static EVENT_ALL_MENU_MIX_SERVER_ACTOR_CAN = 11310
    public static EVENT_ALL_MENU_MIX_SERVER_COUNTRY_JOIN = 11311
    public static EVENT_ALL_MENU_MIX_SERVER_COUNTRY_CAN = 11312
    public static EVENT_ALL_MENU_NEW_ESCORT_LIST = 11314
    public static EVENT_ALL_MENU_SHOPPLAYER_STORE_VIP = 11315
    public static EVENT_ALL_MENU_TITLE_DOING = 11317
    public static EVENT_ALL_VIEW_WORLD_SPRITE_GUIDE = 11318
    public static EVENT_ALL_VIEW_WORLD_DEL_AUTO_FIRE = 11319
    public static EVENT_ALL_ALL_VIEW_WORLD_SHOP = 11321
    public static EVENT_ALL_SKY_ARENA_MENU = 11322
    public static EVENT_ALL_COUNTRY_BOSS = 11324
    public static EVENT_ALL_COUNTRY_BOSS_ACTIVATE = 11326
    public static EVENT_ALL_MENU_CSS = 23723
    public static EVENT_ALL_MENU_END = 11500
    public static EVENT_RECYCLE_SHOP = 1888
    public static EVENT_ARENA_NEW_LEVEL1_MAP = 1901
    public static EVENT_ARENA_NEW_LEVEL2_MAP = 1902
    public static EVENT_ARENA_NEW_LEVEL3_MAP = 1903
    public static EVENT_ARENA_NEW_LEVEL4_MAP = 1904
    public static EVENT_ARENA_NEW_LEVEL5_MAP = 1905
    public static EVENT_ARENA_NEW_LEVEL6_MAP = 1906
    public static EVENT_ARENA_NPC_MAP = 1907
    public static EVENT_ARENA_NEW_MAP = 1900
    public static EVENT_ALL_WAR_ADD_ARMY = 30050
    public static EVENT_ALL_WAR_ARMY_DETAIL = 30051
    public static EVENT_ALL_WAR_ARMY_LIST = 30053
    public static EVENT_ALL_WAR_SOLDIER_ARRAY = 30054
    public static EVENT_ALL_WAR_ARMY_MENU = 30055
    public static EVENT_ALL_WAR_POWER_MENU = 30056
    public static EVENT_ALL_WAR_QUIT_MENU = 30057
    public static EVENT_ALL_WAR_EMBATTLE = 30059
    public static EVENT_ALL_WAP_SCHEME = 30061
    public static EVENT_ALL_WAR_ARMY_REMOVE = 30062
    public static EVENT_ALL_WAR_ARMY_INSERT = 30063
    public static EVENT_ALL_WAR_ARMY_DELETE = 30064
    public static EVENT_ALL_WAR_ARMY_BUILD_LIST = 30069
    public static EVENT_ALL_WAR_VIEW_COUNTRY = 30070
    public static EVENT_ALL_ARMY_VIEW_DECLARE_COUNTRY = 30100
    public static EVENT_ALL_ARMY_AGREE_DECLARE_MONEY = 30101
    public static EVENT_ALL_ARMY_REFUSE_DECLARE_MONEY = 30102
    public static EVENT_ALL_SOLDIER_REMOVE = 30104
    public static EVENT_ALL_WAR_WIN_DO_DESTORY = 30106
    public static EVENT_ALL_UNION_DEL_MENBER = 30107
    public static EVENT_ALL_UNION_CHANGE = 30108
    public static EVENT_ALL_UNION_APPLY_HELP = 30110
    public static EVENT_ALL_TEAMBOSS_ENTER_FIGHT = 30201
    public static EVENT_ALL_TEAMBOSS_CONTINUE_FIGHT = 30202
    public static EVENT_ALL_TEAMBOSS_QUIT_FIGHT = 30203
    public static EVENT_BAG_GRID_PANEL = 34
    public static EVENT_EQUIP_SHOP_BAG_WINDOW = 41
    public static EVENT_BATTLE_ORDER_ATTACK = 2
    public static EVENT_BATTLE_ORDER_SKILL = 3
    public static EVENT_BATTLE_ORDER_ITEM = 4
    public static EVENT_BATTLE_ORDER_AUTO = 6
    public static EVENT_BATTLE_ORDER_ESCAPE = 7
    public static EVENT_BATTLE_ORDER_CHAT_ROOM = 8
    public static EVENT_BATTLE_ORDER_CHAT_INPUT = 9
    public static EVENT_BATTLE_ORDER_CHAT_INFO = 10
    public static EVENT_COUNTRY_BUTTON_OpenRecruit = 7903
    public static EVENT_COUNTRY_BUTTON_KING_PASS = 7904
    public static EVENT_COUNTRY_BUTTON_TaxRate = 7905
    public static EVENT_COUNTRY_BUTTON_EnterRate = 7906
    public static EVENT_SERVICER_EMAIL_DEL = 11404
    public static EVENT_COUNTRY_DONATE_TAB_MONEY1 = 10301
    public static EVENT_COUNTRY_DONATE_TAB_WOOD = 10303
    public static EVENT_WAR_ARMY_TAB_FIGHT = 13111
    public static EVENT_WAR_ARMY_TAB_READY = 13112
    public static SUB_ACTIVITY_LIST = 0
    public static SUB_INFO_DATA_LIST = 1
    public static EVENT_UNIONLIB_MYUNION_LEAVE = 14204
    public static EVENT_UNIONLIB_MYUNION_LIB_YES = 14218
    public static EVENT_UNIONLIB_MYUNION_LIB_NO = 14219
    public static EVENT_UNIONLIB_MYUNION_LIB_VIEW = 14220
    public static EVENT_WARTOP_TAB_PLAYER = 14002
    public static EVENT_WARTOP_TAB_COUNTRYPLAYER = 14003
    public static EVENT_WARTOP_TAB_COUNTRY = 14004
    public static EVENT_LOTTERY_DRAW_LIST_TAB_NOW = 20902
    public static EVENT_LOTTERY_DRAW_LIST_TAB_HISTORY = 20903
    public static EVENT_BATTLE_3V3 = 5e4
    public static EVENT_BATTLE_5V5 = 50001
    public static EVENT_BATTLE_3V3_WORLD = 50002
    public static EVENT_BATTLE_5V5_WORLD = 50003
    public static EVENT_CROSS_DUNGEON_JOIN_TEAM = 50101
    public static SUB_TYPE_COUNTRY_STATUS_OTHER_PLAYER = 2
    public static SUB_TYPE_CHOICE_COUNTRY_LIST = 18
    public static SUB_TYPE_CHOICE_COUNTRY_RANK = 20
    public static SUB_TYPE_CHOICE_COUNTRY_GRADE = 23
    public static SUB_WORLD_ITEM_LIST = 0
    public static SUB_PET_ITEM_LIST = 1
    public static SUB_PET_ITEM_RESET2 = 2
    public static SUB_PET_ITEM_RESET = 3
    public static SUB_PET_ITEM_ADD_SKILL = 4
    public static SUB_PET_ITEM_AGE = 5
    public static SUB_BATTLE_ITEM_LIST = 6
    public static SUB_STORAGE_PLAYER = 0
    public static SUB_STORAGE_COUNTRY = 1
    public static SUB_STORAGE_COUNTRY_SYSTEM = 2
    public static SUB_STORAGE_OTHER_PLAYER = 3
    public static SUB_STORAGE_PLAYER_VIP = 4
    public static SUB_STORAGE_OTHER_PLAYER_VIP = 5
    public static SUB_MISSION_NPC_MENU = 1
    public static SUB_MISSION_OFFLINE_LIST = 2
    public static SUB_MISSION_OFFLINE_INFO = 3
    public static SUB_MISSION_ESCORT_INFO = 4
    public static SUB_MISSION_DIRECT_STEP1 = 5
    public static SUB_MISSION_DIRECT_STEP2 = 6
    public static SUB_MISSION_ACCEPT = 7
    public static SUB_MISSION_DOING = 8
    public static SUB_MISSION_VIEW = 9
    public static SUB_MISSION_SUBMIT_NORMAL = 10
    public static SUB_MISSION_MAIL = 11
    public static SUB_MISSION_ONE_ACCEPT = 12
    public static SUB_MISSION_ONE_SUBMIT = 13
    public static SUB_RANDOM_MISSION_CHANGE = 14
    public static SUB_SPRITE_GUIDE = 15
    public static SUB_TYPE_CHOICE_WAR_LIST = 24
    public static SUB_TYPE_POLL_LIST = 41
    public static EVENT_OPEN_USER_CENTER = 9e6
    public static EVENT_OPEN_USER_AGREEMENT = 9000001
    public static EVENT_OPEN_USER_POLICY = 9000002
    public static EVENT_ACCOUNT_BIND = 9000003
    public static EVENT_OPEN_CUSTOMER_SERVICE = 9000004
    public static EVENT_DEL_ACCOUNT = 9000005
    public static EVENT_CHANGE_PASSWORD = 9000006
    public static EVENT_CHANGE_BIND = 9000008
    public static PLAYER_INFO_WORLD = 0
    public static PLAYER_INFO_BATTLE = 1
    public static isMainShowing = false
    public static isDelay = false
    public static SUB_SHOP_COMBIN = 0
    public static SUB_SHOP_INTEGRAL = 1
    public static SUB_SHOP_ARENA = 2
    public static SUB_SHOP_SKYARENA = 3
    public static SUB_SHOP_PETCOMPOSITE = 4
    public static SUB_SHOP_TOWER = 5
    public static SUB_SHOP_PLA = 6
    public static SUB_SHOP_CAMPWAR = 7
    public static SUB_SHOP_TITLLE_INDEX = 0
    public static _isSkyArenaClose = -1
    public static skyArenaType = 0
    public static CROSS_ONE_ARENA_SELECT = 2
    public static unifiedKey = [
        {
            lab: '聊天',
            parameter: 0
        },
        {
            lab: '背包',
            parameter: 1
        },
        {
            lab: '宠物',
            parameter: 2
        },
        {
            lab: '人物',
            parameter: 3
        },
        {
            lab: '邮件',
            parameter: 4
        }
    ]
    public static corssNewKey = [
        {
            lab: '背包',
            parameter: 1
        },
        {
            lab: '宠物',
            parameter: 2
        }
    ]
    public static ACTIVITY_WELFARE = 1
    public static roleUserType = ListPlayerVo.UI_INTO_NEAR_LIST
    public static RESOLVE_MENU_LEVEL = 20
    public static SETTING_TEXT = [
        ['显示名字', '简单'],
        ['小地图', '简单'],
        ['显示玩家', '简单'],
        ['对方申请入队', '手动接受'],
        ['对方邀请入队', '手动接受'],
        ['聊天显示', '1行'],
        ['对方拜师', '手动接受'],
        ['地图资源(跳图生效)', '默认'],
        ['动画资源(跳图生效)', '默认'],
        ['角色形象(跳图生效)', '默认'],
        ['玩家数量(跳图生效)', '全部'],
        ['', ''],
        ['', ''],
        ['', ''],
        ['', ''],
        ['', ''],
        ['', ''],
        ['', ''],
        ['', ''],
        ['', ''],
        ['对方邀请入队', '自动拒绝'],
        ['对方申请入队', '自动接受'],
        ['对方申请入队', '自动拒绝'],
        ['世界频道', '关'],
        ['区域频道', '关'],
        ['国家频道', '关'],
        ['队伍频道', '关'],
        ['私聊频道', '关'],
        ['坐骑显示', '关'],
        ['宠物显示', '关'],
        ['宠物指令', '关'],
        ['聊天显示', '保留'],
        ['聊天显示', '3行'],
        ['聊天显示', '关'],
        ['对方拜师', '自动拒绝'],
        ['音乐', '关'],
        ['盟国频道', '关'],
        ['', ''],
        ['小地图', '详细'],
        ['小地图', '关'],
        ['显示名字', '详细'],
        ['显示名字', '关'],
        ['显示玩家', '保留'],
        ['显示玩家', '详细'],
        ['显示玩家', '关'],
        ['地图资源(跳图生效)', '简单'],
        ['地图资源(跳图生效)', '关闭'],
        ['精灵助手', '关'],
        ['新手教程', '关'],
        ['玩家数量(跳图生效)', '50人'],
        ['玩家数量(跳图生效)', '20人'],
        ['动画资源(跳图生效)', '简单'],
        ['角色形象(跳图生效)', '简单']
    ]
    public static SETTING_1 = [
        [UIHandler.EVENT_ALL_SETTING_JOIN_ACCEPT, UIHandler.EVENT_SETTING_SET_JOIN, UIHandler.EVENT_ALL_SETTING_JOIN_REJECT],
        [UIHandler.EVENT_SETTING_SET_INVITE, UIHandler.EVENT_ALL_SETTING_INVITE_REJECT],
        [UIHandler.EVENT_SETTING_MASTER_ACCEPT, UIHandler.EVENT_ALL_SETTING_MASTER_REJECT],
        [UIHandler.EVENT_ALL_SETTING_PET_PLAN_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_SHOW_CHAT_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_CHAT_WORLD_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_CHAT_MAP_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_CHAT_COUNTRY_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_CHAT_TEAM_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_CHAT_PRIVATE_OFF, 0],
        [UIDefine.EVENT_ALL_SETTING_SHOW_CHAT_UNION, 0],
        [UIHandler.EVENT_ALL_SETTING_MUSIC_BACKGROUND_OFF, 0],
        [UIHandler.EVENT_ALL_SETTING_MUSIC_EFFECT_OFF, 0]
    ]
    public static EXPLAIN_1 = [
        '对方申请入队',
        '对方邀请入队',
        '对方拜师',
        '开: 手动下达宠物指令\n关: 宠物自动攻击',
        '开: 显示精灵助手\n关: 关闭精灵助手',
        '聊天显示行数',
        '世界聊天',
        '区域聊天',
        '国家聊天',
        '队伍聊天',
        '私聊',
        '盟国聊天',
        '音乐',
        '新手教程'
    ]
    public static TAB_EXPLAIN_1 = '/cffff00宠物战斗指令/p\n开启: 手动下达宠物指令\n关闭: 宠物自动攻击'
    public static SETTING_MENU = UIHandler.SETTING_1
    public static SETTING_EXPLAIN = UIHandler.EXPLAIN_1
}

export class UseByOneKey {
    public static creatInputNumUI(e) {
        console.log('一键使用 输入使用数量')
    }
}
