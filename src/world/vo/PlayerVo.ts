
import { MainEvent } from "../main/define/MainEvent";
import { PetEvent } from "../pet/define/PetEvent";
import { MainViewType } from "../viewType/MainViewType";
import { ModName } from "../viewType/ModName";
import GameWorld from "../GameWorld";
import { SceneViewType } from "../viewType/SceneViewType";
import { ProxyType } from "../viewType/ProxyType";
import MapData from "../scene/data/map/MapData";
import GameText from "../gameCore/GameText";
import PowerString from "../gameCore/PowerString";
import { Tool } from "../gameCore/Tool";
import StringBuffer from "../gameCore/StringBuffer";
import { RoleEvent } from "../role/define/RoleEvent";
import SpecialIconUtil from "../gameCore/SpecialIconUtil";
import { RoleGodData } from "../role/data/RoleGodData";
import { MissionModel } from "../mission/data/MissionModel";
import MissionDef from "../mission/define/MissionDef";
import { MissionEvent } from "../mission/define/MissionEvent";

import { BattleDefine } from "../battle/define/BattleDefine";
import { Buffer } from "../battle/data/Buffer";
import facade from "../gameCore/Facade";
import PlayerTurnMonster from "../PlayerTurnMonster";
import ModelVo from "./ModelVo";

export default class PlayerVo extends ModelVo {
    public static MAX_LEARN_SKILL_SIZE = 20
    public static skillShieldArr = [5900, 5980]
    public static MAX_AUTO_SKILL_COUNT = 4
    public static BSTATUS_ESCAPE = 1

    public gerenState: number
    protected _skillList_1: Skill[]
    public formationList: any[]
    public formationSkill: any
    public autoSkillID_Initiative: number
    public autoSkillID: number[]
    public missionList: Mission[]
    public argo: number
    protected _exp2: number
    protected _expMax2: number
    protected _exp: number
    protected _expMax: number
    protected _hp: number
    protected _mp: number
    public cp: number
    protected _sp: number
    public str: number
    public con: number
    public agi: number
    public ilt: number
    public wis: number
    protected _money1: number
    protected _money2: number
    protected _money3: number
    public numBag: number
    public numStroe: number
    public countryHonor: number
    public cityId: number
    public killCount: number
    public Pkwincount: number
    public Pklosecount: number
    public totalOnline: number
    public masterFlag: number
    public partnerId: number
    public partnerName: string
    public integral: number
    public arenaPoint: number
    public towerPoint: number
    public arenaStutas: number
    public plaPoint: number
    public campWarPoint: number
    protected _richManPoint: number
    protected _redPacketPoint: number
    public skyarenaPoint: number
    protected _combatPoint: number
    protected _hpMax: number
    protected _mpMax: number
    public crossHpMax: number
    public crossMpMax: number
    public crossHp: number
    public crossMp: number
    public speed: number
    public atkMin: number
    public atkMax: number
    public atk_time: number
    public atk_str: number
    public atk_agi: number
    public atk_magic: number
    public def_str: number
    public def_agi: number
    public def_magic: number
    public dodge: number
    public hitrate: number
    public hitMagic: number
    public critical: number
    public forceHit: number
    public wil: number
    public tough: number
    public block: number
    public brkArmor: number
    public magic_penetration: number
    public insight: number
    public def_field: number
    public back: number
    public magic_back: number
    public life_absorption: number
    public mana_absorption: number
    public heal_recovery: number
    public mana_recovery: number
    public recovery: number
    // 免伤护盾数据，战斗中使用
    public keepout_atk_time: number
    public exp_up: number
    public expExpireTime: number
    public ignore_back: number
    public ignore_magic_back: number
    public ignore_block: number
    public ignore_insight: number
    public ignore_wil: number
    public ignore_touch: number
    public ignoreCritical: number
    public criticalDmg: number
    public atk_str_range: number
    public atk_str_nearby: number
    public atk_agi_range: number
    public atk_agi_nearby: number
    public def_str_range: number
    public def_str_nearby: number
    public def_agi_range: number
    public def_agi_nearby: number
    public reflection: number
    public titlePower1: number
    public titlePowerValue1: number
    public titlePower2: number
    public titlePowerValue2: number
    public power: number
    public powerValue: number
    public powerExpireTime: number
    public countrypowerValue: number
    protected _missionCntMap: any
    public readAddValue: number
    // 套装数据
    public itemSetData: number[]
    public _illusionInfo: { [key: number]: any }
    public putIllusion: any
    public bStatus: number
    public autoMoveEndPt: egret.Point
    private bagSize: number

    public bag: PlayerBag
    public playerTurnMonster: PlayerTurnMonster
    protected _hpDisplay: number
    protected _mpDisplay: number
    protected _isAutoMoving: boolean
    public autoMovePath
    public fightPowerList
    public battleBufferList: Vector<Buffer>
    public position: number
    public helpCountry
    public lovePlayer
    protected _obj
    public worldMer
    public gridX
    public gridY

    public battleNum
    public serviceId
    public serviceName
    public isBattle
    public newReflection: number

    public missionStatus

    public bornStatus: number

    public serverId: number
    public serverName: string

    // 技能槽数量
    public skillCnt: number
    // 拥有者，用于宠物，佣兵，伙伴
    public ownerId: number

    constructor() {
        super(MODEL_TYPE.TYPE_PLAYER)
        this.gerenState = 0;
        this._skillList_1 = [];
        this.formationList = [];
        this.formationSkill = null;
        this.autoSkillID_Initiative = -1;
        this.argo = 0;
        this._exp2 = 0;
        this._expMax2 = 0;
        this._exp = 0;
        this._expMax = 0;
        this._hp = 0;
        this._mp = 0;
        this.cp = 0;
        this._sp = 0;
        this.str = 0;
        this.con = 0;
        this.agi = 0;
        this.ilt = 0;
        this.wis = 0;
        this._money1 = 0;
        this._money2 = 0;
        this._money3 = 0;
        this.numBag = 0;
        this.numStroe = 0;
        this.countryHonor = 0;
        this.cityId = 0;
        this.killCount = 0;
        this.Pkwincount = 0;
        this.Pklosecount = 0;
        this.masterFlag = 0;
        this.totalOnline = 0;
        this.partnerId = 0;
        this.partnerName = "";
        this.integral = 0;
        this.arenaPoint = 0;
        this.towerPoint = 0;
        this.arenaStutas = 0;
        this.plaPoint = 0;
        this.campWarPoint = 0;
        this._richManPoint = 0;
        this._redPacketPoint = 0;
        this.skyarenaPoint = 0;
        this._combatPoint = 0;
        this._hpMax = 0;
        this._mpMax = 0;
        this.crossHpMax = 0;
        this.crossMpMax = 0;
        this.crossHp = 0;
        this.crossMp = 0;
        this.speed = 0;
        this.atkMin = 0;
        this.atkMax = 0;
        this.atk_time = 0;
        this.atk_str = 0;
        this.atk_agi = 0;
        this.atk_magic = 0;
        this.def_str = 0;
        this.def_agi = 0;
        this.def_magic = 0;
        this.dodge = 0;
        this.hitrate = 0;
        this.hitMagic = 0;
        this.critical = 0;
        this.forceHit = 0;
        this.wil = 0;
        this.tough = 0;
        this.block = 0;
        this.brkArmor = 0;
        this.magic_penetration = 0;
        this.insight = 0;
        this.def_field = 0;
        this.back = 0;
        this.magic_back = 0;
        this.life_absorption = 0;
        this.mana_absorption = 0;
        this.heal_recovery = 0;
        this.mana_recovery = 0;
        this.recovery = 0;
        this.keepout_atk_time = 0;
        this.exp_up = 0;
        this.expExpireTime = 0;
        this.ignore_back = 0;
        this.ignore_magic_back = 0;
        this.ignore_block = 0;
        this.ignore_insight = 0;
        this.ignore_wil = 0;
        this.ignore_touch = 0;
        this.ignoreCritical = 0;
        this.criticalDmg = 0;
        this.atk_str_range = 0;
        this.atk_str_nearby = 0;
        this.atk_agi_range = 0;
        this.atk_agi_nearby = 0;
        this.def_str_range = 0;
        this.def_str_nearby = 0;
        this.def_agi_range = 0;
        this.def_agi_nearby = 0;
        this.titlePower1 = 0;
        this.titlePowerValue1 = 0;
        this.titlePower2 = 0;
        this.titlePowerValue2 = 0;
        this.power = 0;
        this.powerValue = 0;
        this.powerExpireTime = 0;
        this.countrypowerValue = 0;
        this._missionCntMap = {};
        this.readAddValue = 0;
        this._illusionInfo = {};
        this.putIllusion = {};
        this.bStatus = 0;
        this.autoMoveEndPt = new egret.Point();
        this.missionList = new Array(Define.MAX_MISSION_SIZE);
    }


    get BagSize() {
        return this.bagSize ? this.bagSize : 0;
    }

    set BagSize(val: number) {
        this.bagSize = val
        if (null == this.bag) {
            this.bag = new PlayerBag(this)
        }
        this.bag.bagSize = val
        this.bag.bagEnd = PlayerBag.EQUIP_POS_END + this.bagSize - 1
    }

    get skillList() {
        return this._skillList_1;
    }

    set skillList(e) {
        this._skillList_1 = e;
    }

    get hpDisplay() {
        return this._hpDisplay;
    }

    set hpDisplay(e) {
        this._hpDisplay = e;
        this.onAttrUpdate(proto.ModelConst.Type.HP_DISPLAY);
    }

    get mpDisplay() {
        return this._mpDisplay;
    }

    set mpDisplay(e) {
        this._mpDisplay = e;
        this.onAttrUpdate(proto.ModelConst.Type.MP_DISPLAY);
    }

    get exp2() {
        return this._exp2;
    }

    set exp2(e) {
        this._exp2 = 0 | +e;
        this.onAttrUpdate(proto.ModelConst.Type.EXP2);
    }

    get expMax2() {
        return this._expMax2;
    }

    set expMax2(e) {
        this._expMax2 = 0 | +e;
        this.onAttrUpdate(proto.ModelConst.Type.EXPMAX2);
    }

    get exp() {
        return this._exp;
    }

    set exp(e) {
        this._exp = 0 | +e;
        this.onAttrUpdate(proto.ModelConst.Type.EXP);
    }

    get expMax() {
        return this._expMax;
    }

    set expMax(e) {
        this._expMax = 0 | +e;
        this.onAttrUpdate(proto.ModelConst.Type.EXPMAX);
    }

    get hp() {
        return this._hp;
    }

    set hp(e) {
        this._hp = 0 | +e;
        this.onAttrUpdate(proto.ModelConst.Type.HP);
        this.isMyPlayer && facade.sendNt(MainEvent.ROLE_HP);
    }

    get mp() {
        return this._mp;
    }

    set mp(e) {
        this._mp = 0 | +e;
        this.onAttrUpdate(proto.ModelConst.Type.MP);
    }

    get sp() {
        return this._sp;
    }

    set sp(e) {
        this._sp = e;
        this.type == MODEL_TYPE.TYPE_PET && facade.sendNt(PetEvent.PET_SKILL_POINT)
    }

    get money1() {
        return this._money1
    }

    set money1(v: number) {
        this._money1 = v
        this.onAttrUpdate(proto.ModelConst.Type.MONEY1)
    }

    get money2() {
        return this._money2
    }

    set money2(v: number) {
        this._money2 = v
        this.onAttrUpdate(proto.ModelConst.Type.MONEY2)
    }

    get money3() {
        return this._money3
    }

    set money3(v: number) {
        this._money3 = v
        this.onAttrUpdate(proto.ModelConst.Type.MONEY3)
    }

    set richManPoint(e) {
        this._richManPoint = e;
        facade.sendNt(MainEvent.ON_PLAYER_ATTR_UPDATE, Define.RICHMAN_SCORE);
    }

    get redPacketPoint() {
        return this._redPacketPoint;
    }

    set redPacketPoint(e) {
        this._redPacketPoint = e;
        facade.sendNt(MainEvent.ON_PLAYER_ATTR_UPDATE, Define.RED_PACKET);
    }

    get combatPoint() {
        return this._combatPoint;
    }

    set combatPoint(e) {
        if (0 == this._combatPoint) {
            this._combatPoint = e
            return
        }
        //this.isMyPlayer && (go.rolePower = e);
        var t = this._combatPoint,
            i = e != t;
        this._combatPoint = e;
        i && this.isMyPlayer && (facade.sendNt(MainEvent.ON_PLAYER_ATTR_UPDATE, proto.ModelConst.Type.COMBATPOINT), facade.showView(ModName.Main, MainViewType.PowerTips, {
            oldValue: t,
            newValue: e
        }));
    }

    get hpMax() {
        return this._hpMax;
    }

    set hpMax(e) {
        this._hpMax = e;
        this.onAttrUpdate(proto.ModelConst.Type.HPMAX);
    }

    get mpMax() {
        return this._mpMax;
    }

    set mpMax(e) {
        this._mpMax = e;
        this.onAttrUpdate(proto.ModelConst.Type.MPMAX);
    }

    get illusionInfo() {
        return this._illusionInfo;
    }

    set illusionInfo(e) {
        this._illusionInfo = e;
    }

    get isAutoMoving() {
        return this._isAutoMoving;
    }

    set isAutoMoving(e) {
        this._isAutoMoving = e;
        e ? facade.showView(ModName.Scene, SceneViewType.AutoMoveTips) : (this.autoMoveEndPt.setTo(0, 0), this.autoMovePath = null, facade.hideView(ModName.Scene, SceneViewType.AutoMoveTips));
    }


    public GenreIsOn() {
        return 1 == this.gerenState;
    }

    public isStateFull() {
        var e = 0 == this.hpMax ? this.get(proto.ModelConst.Type.HPMAX) : this.hpMax,
            t = 0 == this.mpMax ? this.get(proto.ModelConst.Type.MPMAX) : this.mpMax,
            i = this.pet,
            o = true;
        if (GameWorld.myPlayer.id == this.id) {
            var n = facade.getProxy(ModName.Legion, ProxyType.Legion);
            o = n.checkNowActorIsStateFull();
        }
        return this.hp >= e && this.mp >= t && (!i || i.isStateFull()) && o;
    }

    public static setItemSetDataKey(setId: number, cnt: number) {
        return (4095 & setId) << 4 | 15 & cnt;
    }

    public static getItemSetID(setData: number) {
        return setData >> 4 & 4095;
    }

    public static getItemSetNum(setData: number) {
        return 15 & setData;
    }

    public setPosition(t, i) {
        MapData.ins.isBlock(t, i) || super.setPosition(t, i);
    }

    public getMissionCntInfo(e) {
        return this._missionCntMap[e];
    }

    public clearMissionCnt() {
        if (this._missionCntMap) for (var e in this._missionCntMap) this._missionCntMap[e].compCnt = 0;
    }

    public isPower() {
        return !(255 == this.power || this.power <= 0);
    }

    public showBuffIcon() {
        var e = this;
        return e.isPower() || e.isHasCountrypower() || null != e.playerTurnMonster || null != e.formationSkill;
    }

    public isHasCountrypower() {
        return this.countrypowerValue > 0;
    }

    public clearFightPower() {
    }

    public addFightPower(e, t) {
        var i = [e, t];
        this.fightPowerList || this.clearFight();
        this.fightPowerList.push(i);
    }

    public clearFight() {
        this.fightPowerList = GUtil.createTwoArray(0, 0);
    }

    public getCountryBuffer(e) {
        return e == Define.POWER_STR_PERCENT || e == Define.POWER_CON_PERCENT || e == Define.POWER_AGI_PERCENT || e == Define.POWER_ILT_PERCENT || e == Define.POWER_WIS_PERCENT ? this.countrypowerValue : 0;
    }

    public getPKWin() {
        if (this.Pkwincount + this.Pklosecount <= 0) return "0%";
        var e = 100 * this.Pkwincount / (this.Pkwincount + this.Pklosecount);
        return GUtil.intNum(e) + "%";
    }

    public getMasterFlag() {
        return this.masterFlag == Define.MASTER_FLAG_MASTER ? GameText.STR_LIST_DESC_RELATION_MASTER : this.masterFlag == Define.MASTER_FLAG_PRENTICE ? GameText.STR_LIST_DESC_RELATION_PRENTICE : GameText.STR_LIST_NULL;
    }

    /**
     * 从服务器数据中初始化
     * @param info
     * @returns
     */
    public setBy(info: proto.IPlayerInfo) {
        if (!info) return
        this.setId(info.id)
        this.setName(info.name)
        this.setInfo("")
        this.setTitle("")
        this.setIcon1(info.attr.icon1)
        this.setIcon2(info.attr.icon2)
        this.setIcon3(info.attr.icon3)
        this.setSex(this.getSex())
        this.setJob(this.getJob())
        this.setRace(this.getRace())
        this.setLevel(info.attr.level)
        this.setLevel2(info.attr.level2)

        this.setSetting(info.setting)
        this.setStatus(info.attr.status)
        this.setMode(info.mode)
        this.setCountryId(0)
        // 这里要根据等级去读取配置
        this.exp2 = info.attr.exp2
        this.exp = info.attr.exp
        if (this.level2 > 0) {
            this.expMax2 = CfgHelper.getLevelUpExp(this.level2) * 2
        } else {
            this.expMax = CfgHelper.getLevelUpExp(this.level)
        }
        this.hp = info.attr.hp
        this.mp = info.attr.mp
        this.cp = info.attr.cp
        this.str = info.attr.str
        this.con = info.attr.con
        this.agi = info.attr.agi
        this.ilt = info.attr.ilt
        this.wis = info.attr.wis

        // 技能相关
        this.skillList = []
        if (info.skill) {
            this.sp = info.skill.sp
            this.skillCnt = info.skill.cnt

            const skillAry = info.skill.list
            if (skillAry) {
                const ary = Object.values(skillAry).sort((a, b) => a.id - b.id)
                for (const skillData of ary) {
                    const skill = Skill.fromPb(skillData)
                    this.skillList.push(skill)
                }
            }
            // 自动相关
            this.autoSkillID = info.skill.autoSkillId
            this.autoSkillID_Initiative = info.skill.activeAutoSkillId
        }

        if (info.task) {
            const ary = info.task.tasks
            const state = info.task.taskStatus

            this.missionStatus = state
            for (const t of ary) {
                const missionCfg = CfgHelper.getMissionCfg(t.id)
                const ms = Mission.fromCfg(missionCfg)
                if (t.cond && t.cond.length) {
                    const submit = ms.submitCondition
                    for (const submitCon of submit) {
                        if (!Condition.isNeedRecord(submitCon.type)) continue
                        const tmp = t.cond.shift()
                        submitCon.currentNum = tmp.num
                    }
                }
                this.addMission(ms)
            }
        }

        if (info.petId) {
            this.petId = info.petId as Long
        }
    }

    public fromBytes(e, t) {
        t.setId(e.getInt());
        t.setName(e.getString());
        t.setInfo(e.getString());
        t.setTitle(e.getString());
        t.setSex(e.getByte());
        t.setRace(e.getByte());
        t.setJob(e.getByte());
        t.setLevel(e.getByte());
        t.setLevel2(e.getByte());
        t.exp2 = e.getInt();
        t.expMax2 = e.getInt();
        t.setIcon1(e.getLong());
        t.setIcon2(e.getLong());
        t.setIcon3(e.getLong());
        t.setSetting(e.getInt());
        t.setStatus(e.getInt());
        t.setMode(e.getByte());
        var i = e.getInt();
        t.setCountryId(i);
        i >= 0 && (t.setCountryName(e.getString()), t.setCountryRank(e.getByte()));
        t.exp = e.getInt();
        t.expMax = e.getInt();
        t.hp = e.getInt();
        t.mp = e.getInt();
        t.cp = e.getShort();
        t.sp = e.getInt();
        t.str = e.getShort();
        t.con = e.getShort();
        t.agi = e.getShort();
        t.ilt = e.getShort();
        t.wis = e.getShort();
        t.money1 = e.getInt();
        t.money2 = e.getInt();
        t.money3 = e.getInt();
        t.numBag = e.getByte();
        t.numStroe = e.getByte();
        t.countryHonor = e.getInt();
        t.cityId = e.getInt();
        t.killCount = e.getInt();
        t.Pkwincount = e.getInt();
        t.Pklosecount = e.getInt();
        t.masterFlag = e.getByte();
        t.partnerId = e.getInt();
        t.partnerId >= 0 && (t.partnerName = e.getString());
        t.helpCountry = e.getString();
        t.lovePlayer = e.getString();
        t.integral = e.getInt();
        t.arenaPoint = e.getInt();
        t.skyarenaPoint = e.getInt();
        t.combatPoint = e.getInt();
        t.ignore_back = e.getInt();
        t.ignore_back = 0;
        t.ignore_magic_back = e.getInt();
        t.ignore_magic_back = 0;
        t.ignore_block = e.getInt();
        t.ignore_block = 0;
        t.ignore_insight = e.getInt();
        t.ignore_insight = 0;
        t.ignore_wil = e.getInt();
        t.ignore_wil = 0;
        t.ignore_touch = e.getInt();
        t.ignore_touch = 0;
        t.ignoreCritical = e.getInt();
        t.criticalDmg = e.getInt();
        t.atk_str_range = e.getInt();
        t.atk_str_nearby = e.getInt();
        t.atk_agi_range = e.getInt();
        t.atk_agi_nearby = e.getInt();
        t.def_str_range = e.getInt();
        t.def_str_nearby = e.getInt();
        t.def_agi_range = e.getInt();
        t.def_agi_nearby = e.getInt();
        t.setVipLevel(e.getByte());
        t.setVipLevel2(e.getByte());
        t.setSuperQqLevel(e.getByte());
        var o = e.getByte();
        t.autoSkillID = new Array(o);
        for (var n = 0; o > n; n++) t.autoSkillID[n] = e.getShort();
        t.autoSkillID_Initiative = e.getShort();
        t.fromItemSetData(e);
        t.power = e.getShort();
        t.powerValue = e.getShort();
        t.titlePower1 = e.getShort();
        t.titlePowerValue1 = e.getShort();
        t.titlePower2 = e.getShort();
        t.titlePowerValue2 = e.getShort();
        t.countrypowerValue = e.getByte();
        t.setEnchantValue(e.getInt());
        t._missionCntMap || (t._missionCntMap = {});
        for (var a = e.getByte(), n = 0; a > n; ++n) {
            var r = e.getInt(),
                s = e.getInt(),
                l = e.getInt();
            t._missionCntMap[r] = {
                compCnt: s,
                total: l
            };
        }
    }

    // 计算套装信息
    public updateItemSetData() {
        this.itemSetData = []
        const info = {}
        for (let i = PlayerBag.EQUIP_POS_START; i < PlayerBag.EQUIP_POS_END; i++) {
            const equip = this.bag.getItem(i)
            if (!equip) continue
            const setId = equip.getItemSetID()
            if (!setId) continue
            const num = this.bag.getEquipItemSetNum(setId)
            if (num <= 0) continue
            info[setId] = num
        }
        const keys = Object.keys(info)
        if (!keys.length) { return }
        for (const key of keys) {
            const setId = Number(key)
            const cnt = info[key]
            const bit = CfgHelper.getSuit(setId)
            for (let i = 0; i < bit.length;) {
                let reqCnt = bit[i++]
                let powerType = bit[i++]
                let powerValue = bit[i++]
                let vipLvReq = bit[i++]
                let needVip = bit[i++] < 1
                if (cnt >= reqCnt) {
                    if (!needVip || this.getVipLevel() >= vipLvReq) {
                        const packValue = PlayerVo.setItemSetDataKey(setId, info[key])
                        this.itemSetData.push(packValue)
                        this.itemSetData.push(powerType)
                        this.itemSetData.push(powerValue)
                    }
                }
            }

        }
    }

    public fromItemSetData(e) {
        console.error("fromItemSetData .")
        var t = e.getByte();
        this.itemSetData = new Array(t);
        for (var i = 0; t > i; i++) this.itemSetData[i] = e.getShort();
    }

    public setMoneyByType(type: proto.ModelConst.Type, val: number, buffer: StringBuffer = null) {
        switch (type) {
            case proto.ModelConst.Type.MONEY1:
                this.money1 != val && null != buffer && buffer.append(PowerString.makeColorString(GameText.STR_MONEY1 + (val - this.money1), Tool.COLOR_YELLOW) + "\n");
                this.money1 = val;
                break;
            case proto.ModelConst.Type.MONEY2:
                this.money2 != val && null != buffer && buffer.append(PowerString.makeColorString(GameText.STR_MONEY2 + (val - this.money2), Tool.COLOR_MONEY2) + "\n");
                this.money2 = val;
                break;
            case proto.ModelConst.Type.MONEY3:
                this.money3 != val && null != buffer && buffer.append(PowerString.makeColorString(GameText.STR_MONEY3 + (val - this.money3), Tool.COLOR_MONEY3) + "\n");
                this.money3 = val;
        }
    }

    public addValueStr(e, t) {
        var i = this;
        switch (e) {
            case proto.ModelConst.Type.HELP_COUNTRY:
                i.helpCountry = t;
                break;
            case proto.ModelConst.Type.LOVE_PLAYER:
                i.lovePlayer = t;
                break;
            case proto.ModelConst.Type.PARTNER_NAME:
                i.partnerName = t;
        }
    }

    public addValue(type: proto.ModelConst.Type, value: number) {
        var o = 0,
            n = 0;
        value = GUtil.intNum(value)
        this.readAddValue = value
        switch (type) {
            case proto.ModelConst.Type.COMBATPOINT:
                this.combatPoint = value;
                break;
            case proto.ModelConst.Type.PARTNER_ID:
                this.partnerId = value;
                break;
            case proto.ModelConst.Type.MASTER_FLAG:
                this.masterFlag = value;
                break;
            case proto.ModelConst.Type.EXP:
                this.exp += value;
                break;
            case proto.ModelConst.Type.EXP2:
                this.exp2 += value;
                break;
            case proto.ModelConst.Type.SET_EXP:
                this.exp = value;
                break;
            case proto.ModelConst.Type.SET_EXP2:
                this.exp2 = value;
                break;
            case proto.ModelConst.Type.EXPMAX:
                this.expMax = Tool.sumValue(this.expMax, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.SET_EXPMAX:
                this.expMax = value;
                break;
            case proto.ModelConst.Type.SET_EXPMAX2:
                this.expMax2 = value;
                break;
            case proto.ModelConst.Type.HP:
                this.hp = Tool.sumValue(this.hp, value, 0, this.get(proto.ModelConst.Type.HPMAX));
                break;
            case proto.ModelConst.Type.HP_DISPLAY:
                this.hpDisplay = Tool.sumValue(this.hpDisplay, value, 0, this.get(proto.ModelConst.Type.HPMAX));
                break;
            case proto.ModelConst.Type.MP:
                this.mp = Tool.sumValue(this.mp, value, 0, this.get(proto.ModelConst.Type.MPMAX));
                break;
            case proto.ModelConst.Type.MP_DISPLAY:
                this.mpDisplay = Tool.sumValue(this.mpDisplay, value, 0, this.get(proto.ModelConst.Type.MPMAX));
                break;
            case proto.ModelConst.Type.SP:
                this.sp = Tool.sumValue(this.sp, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.CP:
                this.cp = Tool.sumValue(this.cp, value, 0, Tool.MAX_VALUE_short);
                facade.sendNt(RoleEvent.ROLE_ATTR_UPDATE);
                break;
            case proto.ModelConst.Type.STR:
                this.str = Tool.sumValue(this.str, value, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
                o = this.get(proto.ModelConst.Type.HPMAX);
                this.hp > o && (this.hp = o);
                this.hpMax = o;
                break;
            case proto.ModelConst.Type.CON:
                this.con = Tool.sumValue(this.con, value, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
                o = this.get(proto.ModelConst.Type.HPMAX);
                this.hp > o && (this.hp = o);
                this.hpMax = o;
                break;
            case proto.ModelConst.Type.AGI:
                this.agi = Tool.sumValue(this.agi, value, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
                break;
            case proto.ModelConst.Type.ILT:
                this.ilt = Tool.sumValue(this.ilt, value, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
                o = this.get(proto.ModelConst.Type.MPMAX);
                this.mp > o && (this.mp = o);
                this.mpMax = o;
                break;
            case proto.ModelConst.Type.WIS:
                this.wis = Tool.sumValue(this.wis, value, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
                o = this.get(proto.ModelConst.Type.MPMAX);
                this.mp > o && (this.mp = o);
                this.mpMax = o;
                break;
            case proto.ModelConst.Type.MONEY1:
                this.money1 = Tool.sumValue(this.money1, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.MONEY2:
                this.money2 = Tool.sumValue(this.money2, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.MONEY3:
                this.money3 > 0 && value > 0 && this.money3 + value < 0 ? this.money3 = Tool.MAX_VALUE_int : (this.money3 += value, this.money3 < 0 && (this.money3 = 0));
                break;
            case proto.ModelConst.Type.NUM_STROE:
                this.numStroe = Tool.sumValue(this.numStroe, value, 0, proto.ModelConst.Type.MAX_STORE_NUM);
                break;
            case proto.ModelConst.Type.COUNTRY_HONOR:
                this.countryHonor = Tool.sumValue(this.countryHonor, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.KILL_COUNT:
                this.killCount = Tool.sumValue(this.killCount, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.PK_WIN_COUNT:
                this.Pkwincount = Tool.sumValue(this.Pkwincount, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.PK_LOSE_COUNT:
                this.Pklosecount = Tool.sumValue(this.Pklosecount, value, 0, Tool.MAX_VALUE_int);
                break;
            case proto.ModelConst.Type.SPEED:
                n = this.speed;
                this.speed = Tool.sumValue(this.speed, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.speed - n;
                break;
            case proto.ModelConst.Type.ATK_STR:
                n = this.atk_str;
                this.atk_str = Tool.sumValue(this.atk_str, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.atk_str - n;
                break;
            case proto.ModelConst.Type.ATK_TIME:
                n = this.atk_time;
                this.atk_time = Tool.sumValue(this.atk_time, value, 0, proto.ModelConst.Type.MAX_HIT_TIME);
                this.readAddValue = this.atk_time - n;
                break;
            case proto.ModelConst.Type.ATK_AGI:
                n = this.atk_agi;
                this.atk_agi = Tool.sumValue(this.atk_agi, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.atk_agi - n;
                break;
            case proto.ModelConst.Type.DEF_STR_RANGE:
                if (!GameWorld.isPreviewBeta) {
                    this.readAddValue = 0;
                    break;
                }
                n = this.def_str_range;
                this.def_str_range = Tool.sumValue(this.def_str_range, value, 0, Tool.MAX_VALUE_int);
                this.readAddValue = this.def_str_range - n;
                break;
            case proto.ModelConst.Type.DEF_STR_NEARBY:
                if (!GameWorld.isPreviewBeta) {
                    this.readAddValue = 0;
                    break;
                }
                n = this.def_str_nearby;
                this.def_str_nearby = Tool.sumValue(this.def_str_nearby, value, 0, Tool.MAX_VALUE_int);
                this.readAddValue = this.def_str_nearby - n;
                break;
            case proto.ModelConst.Type.DEF_AGI_NEARBY:
                if (!GameWorld.isPreviewBeta) {
                    this.readAddValue = 0;
                    break;
                }
                n = this.def_agi_nearby;
                this.def_agi_nearby = Tool.sumValue(this.def_agi_nearby, value, 0, Tool.MAX_VALUE_int);
                this.readAddValue = this.def_agi_nearby - n;
                break;
            case proto.ModelConst.Type.DEF_AGI_RANGE:
                if (!GameWorld.isPreviewBeta) {
                    this.readAddValue = 0;
                    break;
                }
                n = this.def_agi_range;
                this.def_agi_range = Tool.sumValue(this.def_agi_range, value, 0, Tool.MAX_VALUE_int);
                this.readAddValue = this.def_agi_range - n;
                break;
            case proto.ModelConst.Type.ATK_STR_NEARBY:
                n = this.atk_str_nearby;
                this.atk_str_nearby = Tool.sumValue(this.atk_str_nearby, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.atk_str_nearby - n;
                break;
            case proto.ModelConst.Type.ATK_STR_RANGE:
                n = this.atk_str_range;
                this.atk_str_range = Tool.sumValue(this.atk_str_range, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.atk_str_range - n;
                break;
            case proto.ModelConst.Type.ATK_AGI_RANGE:
                n = this.atk_agi_range;
                this.atk_agi_range = Tool.sumValue(this.atk_agi_range, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.atk_agi_range - n;
                break;
            case proto.ModelConst.Type.ATK_AGI_NEARBY:
                n = this.atk_agi_nearby;
                this.atk_agi_nearby = Tool.sumValue(this.atk_agi_nearby, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.atk_agi_nearby - n;
                break;
            case proto.ModelConst.Type.ATK_MAGIC:
                n = this.atk_magic;
                var r = GameWorld.isPreviewBeta ? 2e6 : proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE;
                this.atk_magic = Tool.sumValue(this.atk_magic, value, 0, r);
                this.readAddValue = this.atk_magic - n;
                break;
            case proto.ModelConst.Type.DEF_STR:
                n = this.def_str;
                var s = GameWorld.isPreviewBeta ? Tool.MAX_VALUE_int : proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE;
                this.def_str = Tool.sumValue(this.def_str, value, 0, s);
                this.readAddValue = this.def_str - n;
                break;
            case proto.ModelConst.Type.DEF_AGI:
                n = this.def_agi;
                var l = GameWorld.isPreviewBeta ? Tool.MAX_VALUE_int : proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE;
                this.def_agi = Tool.sumValue(this.def_agi, value, 0, l);
                this.readAddValue = this.def_agi - n;
                break;
            case proto.ModelConst.Type.DEF_MAGIC:
                n = this.def_magic;
                var u = GameWorld.isPreviewBeta ? Tool.MAX_VALUE_int : proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE;
                this.def_magic = Tool.sumValue(this.def_magic, value, 0, u);
                this.readAddValue = this.def_magic - n;
                break;
            case proto.ModelConst.Type.ARGO:
                this.argo = Tool.sumValue(this.argo, value, -1e5, 1e5);
                break;
            case proto.ModelConst.Type.CRITICAL:
                n = this.critical;
                this.critical = Tool.sumValue(this.critical, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.critical - n;
                break;
            case proto.ModelConst.Type.HIT_MAGIC:
                n = this.hitMagic;
                this.hitMagic = Tool.sumValue(this.hitMagic, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.hitMagic - n;
                break;
            case proto.ModelConst.Type.HIT_RATE:
                n = this.hitrate;
                this.hitrate = Tool.sumValue(this.hitrate, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.hitrate - n;
                break;
            case proto.ModelConst.Type.FORCE_HIT:
                n = this.forceHit;
                this.forceHit = Tool.sumValue(this.forceHit, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.forceHit - n;
                break;
            case proto.ModelConst.Type.DODGE:
                n = this.dodge;
                this.dodge = Tool.sumValue(this.dodge, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.dodge - n;
                break;
            case proto.ModelConst.Type.WIL:
                n = this.wil;
                this.wil = Tool.sumValue(this.wil, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.wil - n;
                break;
            case proto.ModelConst.Type.TOUGH:
                n = this.tough;
                this.tough = Tool.sumValue(this.tough, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.tough - n;
                break;
            case proto.ModelConst.Type.BRK_ARMOR:
                n = this.brkArmor;
                var d = GameWorld.isPreviewBeta ? Tool.MAX_VALUE_int : proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE;
                this.brkArmor = Tool.sumValue(this.brkArmor, value, 0, d);
                this.readAddValue = this.brkArmor - n;
                break;
            case proto.ModelConst.Type.MAGIC_PENETRATION:
                n = this.magic_penetration;
                var c = GameWorld.isPreviewBeta ? Tool.MAX_VALUE_int : proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE;
                this.magic_penetration = Tool.sumValue(this.magic_penetration, value, 0, c);
                this.readAddValue = this.magic_penetration - n;
                break;
            case proto.ModelConst.Type.BLOCK:
                n = this.block;
                this.block = Tool.sumValue(this.block, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.block - n;
                break;
            case proto.ModelConst.Type.DEF_FIELD:
                n = this.def_field;
                this.def_field = Tool.sumValue(this.def_field, value, proto.ModelConst.Type.MIN_DEF_FIELD, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.def_field - n;
                break;
            case proto.ModelConst.Type.INSIGHT:
                n = this.insight;
                this.insight = Tool.sumValue(this.insight, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.insight - n;
                break;
            case proto.ModelConst.Type.BACK:
                n = this.back;
                this.back = Tool.sumValue(this.back, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.back - n;
                break;
            case proto.ModelConst.Type.MAGIC_BACK:
                n = this.magic_back;
                this.magic_back = Tool.sumValue(this.magic_back, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.magic_back - n;
                break;
            case proto.ModelConst.Type.LIFE_ABSORPTION:
                n = this.life_absorption;
                this.life_absorption = Tool.sumValue(this.life_absorption, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.life_absorption - n;
                break;
            case proto.ModelConst.Type.MANA_ABSORPTION:
                n = this.mana_absorption;
                this.mana_absorption = Tool.sumValue(this.mana_absorption, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.mana_absorption - n;
                break;
            case proto.ModelConst.Type.HEAL_RECOVERY:
                n = this.heal_recovery;
                this.heal_recovery = Tool.sumValue(this.heal_recovery, value, proto.ModelConst.Type.MIN_HEAL_RECOVERY, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.heal_recovery - n;
                break;
            case proto.ModelConst.Type.MANA_RECOVERY:
                n = this.mana_recovery;
                this.mana_recovery = Tool.sumValue(this.mana_recovery, value, proto.ModelConst.Type.MIN_MANA_RECOVERY, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.mana_recovery - n;
                break;
            case proto.ModelConst.Type.KEEPOUT_ATK_TIME:
                n = this.keepout_atk_time;
                this.keepout_atk_time = Tool.sumValue(this.keepout_atk_time, value, 0, proto.ModelConst.Type.MAX_KEEPOUT_ATK_TIME);
                this.readAddValue = this.keepout_atk_time - n;
                break;
            case proto.ModelConst.Type.IGNORE_CRITICAL:
                n = this.ignoreCritical;
                this.ignoreCritical = Tool.sumValue(this.ignoreCritical, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.ignoreCritical - n;
                break;
            case proto.ModelConst.Type.CRITICAL_DAMAGE:
                n = this.criticalDmg;
                this.criticalDmg = Tool.sumValue(this.criticalDmg, value, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                this.readAddValue = this.criticalDmg - n;
        }
        super.addValue(type, value);
    }

    public getOld(t) {
        var i = 0;
        switch (t) {
            case proto.ModelConst.Type.HPMAX:
                var o = this.get(proto.ModelConst.Type.LEVEL);
                i = GUtil.intNum(65 * this.get(proto.ModelConst.Type.CON) / 10) + 3 * this.get(proto.ModelConst.Type.STR) + 100 + 40 * (o - 1);
                return this.getPowerValue(i, this._hpMax, Define.POWER_HPMAX, Define.POWER_HPMAX_PERCENT, Define.SKILL_TYPE_PASSIVE, 1, proto.ModelConst.Type.MAX_PLAYER_HP);
            case proto.ModelConst.Type.MPMAX:
                var a = this.get(proto.ModelConst.Type.LEVEL),
                    r = this.get(proto.ModelConst.Type.ILT),
                    s = this.get(proto.ModelConst.Type.WIS);
                i = GUtil.intNum(75 * r / 10) + GUtil.intNum(35 * s / 10) + 50 + 10 * (a - 1);
                return this.getPowerValue(i, this._mpMax, Define.POWER_MPMAX, Define.POWER_MPMAX_PERCENT, Define.SKILL_TYPE_PASSIVE, 1, proto.ModelConst.Type.MAX_PLAYER_MP);
            default:
                return super.get(t);
        }
    }

    public static getAttDesc(e, t) {
        var i = new StringBuffer();
        i.append(e.toString());
        var o = t - e;
        o > 0 ? i.append(PowerString.makeColorString("(+" + o + ")", Tool.COLOR_GREEN)) : 0 > o && i.append(PowerString.makeColorString("(+" + o + ")", Tool.COLOR_RED));
        return i.toString();
    }

    public getPoint(e) {
        var t = 0;
        switch (e) {
            case Define.SKYARENA:
                t = this.skyarenaPoint;
                break;
            case Define.ARENA:
                t = this.arenaPoint;
        }
        return GUtil.intNum(t);
    }

    public get(type: proto.ModelConst.Type) {
        let value = 0;
        switch (type) {
            case proto.ModelConst.Type.INTEGRAL:
                return this.integral | 0
            case proto.ModelConst.Type.EXP:
                return this.exp | 0
            case proto.ModelConst.Type.EXP2:
                return this.exp2 | 0
            case proto.ModelConst.Type.EXPMAX:
                return this.expMax | 0
            case proto.ModelConst.Type.EXPMAX2:
                return this.expMax2 | 0
            case proto.ModelConst.Type.HP:
                return this.hp | 0
            case proto.ModelConst.Type.HP_DISPLAY:
                return this.hpDisplay | 0
            case proto.ModelConst.Type.MP:
                return this.mp | 0
            case proto.ModelConst.Type.MP_DISPLAY:
                return this.mpDisplay | 0
            case proto.ModelConst.Type.CP:
                return this.cp | 0
            case proto.ModelConst.Type.SP:
                return this.sp | 0
            case proto.ModelConst.Type.COMBATPOINT:
                return this.combatPoint | 0
            case proto.ModelConst.Type.STR:
                return this.getBaseValue(this.str, Define.POWER_STR, Define.POWER_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.CON:
                return this.getBaseValue(this.con, Define.POWER_CON, Define.POWER_CON_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.AGI:
                return this.getBaseValue(this.agi, Define.POWER_AGI, Define.POWER_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.ILT:
                return this.getBaseValue(this.ilt, Define.POWER_ILT, Define.POWER_ILT_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.WIS:
                return this.getBaseValue(this.wis, Define.POWER_WIS, Define.POWER_WIS_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.MONEY1:
                return this.money1 | 0
            case proto.ModelConst.Type.MONEY2:
                return this.money2 | 0
            case proto.ModelConst.Type.MONEY3:
                return this.money3 | 0
                break;
            case proto.ModelConst.Type.NUM_STROE:
                return this.numStroe | 0
            case proto.ModelConst.Type.COUNTRY_HONOR:
                return this.countryHonor | 0
            case proto.ModelConst.Type.CITY_ID:
                return this.cityId | 0
            case proto.ModelConst.Type.KILL_COUNT:
                return this.killCount | 0
            case proto.ModelConst.Type.PK_WIN_COUNT:
                return this.Pkwincount | 0
            case proto.ModelConst.Type.PK_LOSE_COUNT:
                return this.Pklosecount | 0
            case proto.ModelConst.Type.TOTAL_ONLINE:
                return this.totalOnline | 0
            case proto.ModelConst.Type.PARTNER_ID:
                return this.partnerId | 0
            case proto.ModelConst.Type.HPMAX:
                let levelHp = this.get(proto.ModelConst.Type.LEVEL) + this.get(proto.ModelConst.Type.LEVEL2)
                let conHp = this.get(proto.ModelConst.Type.CON)
                let strHp = this.get(proto.ModelConst.Type.STR)
                value = GUtil.intNum(65 * conHp / 10) + 3 * strHp + 100 + 40 * (levelHp - 1);
                return this.getPowerValue(value, this._hpMax, Define.POWER_HPMAX, Define.POWER_HPMAX_PERCENT, Define.SKILL_TYPE_PASSIVE, 1, proto.ModelConst.Type.MAX_PLAYER_HP);
            case proto.ModelConst.Type.MPMAX:
                let levelMp = this.get(proto.ModelConst.Type.LEVEL) + this.get(proto.ModelConst.Type.LEVEL2)
                let iltMp = this.get(proto.ModelConst.Type.ILT)
                let wisMp = this.get(proto.ModelConst.Type.WIS)
                value = GUtil.intNum(75 * iltMp / 10) + GUtil.intNum(35 * wisMp / 10) + 50 + 10 * (levelMp - 1);
                return this.getPowerValue(value, this._mpMax, Define.POWER_MPMAX, Define.POWER_MPMAX_PERCENT, Define.SKILL_TYPE_PASSIVE, 1, proto.ModelConst.Type.MAX_PLAYER_MP);
            case proto.ModelConst.Type.SPEED:
                value = 3 * this.get(proto.ModelConst.Type.AGI) + GUtil.intNum(15 * this.get(proto.ModelConst.Type.WIS) / 10);
                return this.getPowerValue(value, this.speed, Define.POWER_SPEED, Define.POWER_SPEED_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.ATK_TYPE:
                if (null == this.bag) return Define.ATKTYPE_STR
                return this.bag.get(Item.EQP_ATK_TYPE)
            case proto.ModelConst.Type.LEFT_WEAPON_TYPE:
                if (!this.bag) return -1
                return this.bag.getForPos(Item.EQP_ITEM_TYPE, PlayerBag.WEAPON_LEFT_POS)
            case proto.ModelConst.Type.RIGHT_WEAPON_TYPE:
                if (!this.bag) return -1
                return this.bag.getForPos(Item.EQP_ITEM_TYPE, PlayerBag.WEAPON_RIGHT_POS)
            case proto.ModelConst.Type.LEFT_ATK_MIN:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_ATK_MIN, PlayerBag.WEAPON_LEFT_POS)
                    value = Math.max(value, 0)
                    value = this.addWeaponSkillDamageOrDur(value, PlayerBag.WEAPON_LEFT_POS)
                }
                return Tool.sumValue(value, 0, 0, proto.ModelConst.Type.MAX_ATK);
            case proto.ModelConst.Type.RIGHT_ATK_MIN:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_ATK_MIN, PlayerBag.WEAPON_RIGHT_POS)
                    value = Math.max(value, 0)
                    value = this.addWeaponSkillDamageOrDur(value, PlayerBag.WEAPON_RIGHT_POS)
                }
                return Tool.sumValue(value, 0, 0, proto.ModelConst.Type.MAX_ATK);
            case proto.ModelConst.Type.ATK_MIN:
                let leftAtkMin = this.get(proto.ModelConst.Type.LEFT_ATK_MIN)
                let rightAtkMin = this.get(proto.ModelConst.Type.RIGHT_ATK_MIN)
                return Math.max(leftAtkMin, rightAtkMin)
            case proto.ModelConst.Type.LEFT_ATK_MAX:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_ATK_MAX, PlayerBag.WEAPON_LEFT_POS)
                    value = Math.max(value, 0)
                    value = this.addWeaponSkillDamageOrDur(value, PlayerBag.WEAPON_LEFT_POS)
                }
                return Tool.sumValue(value, 0, 0, proto.ModelConst.Type.MAX_ATK);
            case proto.ModelConst.Type.RIGHT_ATK_MAX:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_ATK_MAX, PlayerBag.WEAPON_RIGHT_POS)
                    value = Math.max(value, 0)
                    value = this.addWeaponSkillDamageOrDur(value, PlayerBag.WEAPON_RIGHT_POS)
                }
                return Tool.sumValue(value, 0, 0, proto.ModelConst.Type.MAX_ATK);
            case proto.ModelConst.Type.ATK_MAX:
                return Math.max(this.get(proto.ModelConst.Type.LEFT_ATK_MAX), this.get(proto.ModelConst.Type.RIGHT_ATK_MAX))
            case proto.ModelConst.Type.LEFT_ATK_TIME:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_HIT_TIME, PlayerBag.WEAPON_LEFT_POS)
                    if (value < 0) return 0
                }
                return Tool.sumValue(value + 1, 0, proto.ModelConst.Type.MIN_HIT_TIME, proto.ModelConst.Type.MAX_HIT_TIME);
            case proto.ModelConst.Type.RIGHT_ATK_TIME:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_HIT_TIME, PlayerBag.WEAPON_RIGHT_POS)
                    if (value < 0) return 0
                }
                return Tool.sumValue(value + 1, 0, proto.ModelConst.Type.MIN_HIT_TIME, proto.ModelConst.Type.MAX_HIT_TIME);
            case proto.ModelConst.Type.ATK_TIME:
                let equipWeaponTypeAtkTime = this.getEquipWeaponType()
                if (equipWeaponTypeAtkTime == Define.BACK_ERROR_NULL_HAND) {
                    value = 1 + this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HAND_ATK_TIME)
                    value += this.getBagEquipPowerValue(Define.POWER_HAND_ATK_TIME, true)
                } else {
                    value = this.get(proto.ModelConst.Type.LEFT_ATK_TIME) + this.get(proto.ModelConst.Type.RIGHT_ATK_TIME)
                }
                value = this.addWeaponSkillAtkTime(value, equipWeaponTypeAtkTime);
                return Tool.sumValue(value, this.atk_time, proto.ModelConst.Type.MIN_HIT_TIME, proto.ModelConst.Type.MAX_HIT_TIME);
            case proto.ModelConst.Type.ATK_STR:
                value = GUtil.intNum(3 * this.get(proto.ModelConst.Type.AGI) + GUtil.intNum(5 * this.get(proto.ModelConst.Type.STR)));
                return this.getPowerValue(value, this.atk_str, Define.POWER_ATK_STR, Define.POWER_ATK_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE, true);
            case proto.ModelConst.Type.ATK_AGI:
                value = GUtil.intNum(5 * this.get(proto.ModelConst.Type.STR)) + GUtil.intNum(5 * this.get(proto.ModelConst.Type.AGI));
                return this.getPowerValue(value, this.atk_agi, Define.POWER_ATK_AGI, Define.POWER_ATK_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE, true);
            case proto.ModelConst.Type.ATK_MAGIC:
                value = (3 * this.get(proto.ModelConst.Type.ILT) + 2 * this.get(proto.ModelConst.Type.WIS)) * (this.get(proto.ModelConst.Type.HIT_MAGIC) + 900) / 1000;
                return this.getPowerValue(value, this.atk_magic, Define.POWER_ATK_MAGIC, Define.POWER_ATK_MAGIC_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.ATK_STR_NEARBY:
                value = this.get(proto.ModelConst.Type.ATK_STR)
                value += this.getPowerValue(0, 0, Define.POWER_ATK_STR_NEARBY, Define.POWER_ATK_STR_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_STR, Define.POWER_ATK_STR_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_ATK_STR_NEARBY, Define.POWER_ATK_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.ATK_STR_RANGE:
                value = this.get(proto.ModelConst.Type.ATK_STR);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_STR_RANGE, Define.POWER_ATK_STR_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_STR, Define.POWER_ATK_STR_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_ATK_STR_RANGE, Define.POWER_ATK_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.ATK_AGI_NEARBY:
                value = this.get(proto.ModelConst.Type.ATK_AGI);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_AGI_NEARBY, Define.POWER_ATK_AGI_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_AGI, Define.POWER_ATK_AGI_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_ATK_AGI_NEARBY, Define.POWER_ATK_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.ATK_AGI_RANGE:
                value = this.get(proto.ModelConst.Type.ATK_AGI);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_AGI_RANGE, Define.POWER_ATK_AGI_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_ATK_AGI, Define.POWER_ATK_AGI_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_ATK_AGI_RANGE, Define.POWER_ATK_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.DEF_STR:
                value = 8 * this.get(proto.ModelConst.Type.CON);
                if (this.bag) {
                    value += this.bag.get(Item.EQP_DEF_STR)
                }
                return this.getPowerValue(value, this.def_str, Define.POWER_DEF_STR, Define.POWER_DEF_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_DEF);
            case proto.ModelConst.Type.DEF_STR_RANGE:
                value = this.get(proto.ModelConst.Type.DEF_STR);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_STR_RANGE, Define.POWER_DEF_STR_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_STR, Define.POWER_DEF_STR_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_DEF_STR_RANGE, Define.POWER_DEF_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.DEF_STR_NEARBY:
                value = this.get(proto.ModelConst.Type.DEF_STR);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_STR_NEARBY, Define.POWER_DEF_STR_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_STR, Define.POWER_DEF_STR_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_DEF_STR_NEARBY, Define.POWER_DEF_STR_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.DEF_AGI_RANGE:
                value = this.get(proto.ModelConst.Type.DEF_AGI);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_AGI_RANGE, Define.POWER_DEF_AGI_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_AGI, Define.POWER_DEF_AGI_RANGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_DEF_AGI_RANGE, Define.POWER_DEF_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.DEF_AGI_NEARBY:
                value = this.get(proto.ModelConst.Type.DEF_AGI);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_AGI_NEARBY, Define.POWER_DEF_AGI_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                value += this.getPowerValue(0, 0, Define.POWER_DEF_AGI, Define.POWER_DEF_AGI_NEARBY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
                return value + this.getPowerValue(0, 0, Define.POWER_DEF_AGI_NEARBY, Define.POWER_DEF_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.DEF_AGI:
                value = 8 * this.get(proto.ModelConst.Type.CON)
                if (this.bag) {
                    value += this.bag.get(Item.EQP_DEF_AGI)
                }
                return this.getPowerValue(value, this.def_agi, Define.POWER_DEF_AGI, Define.POWER_DEF_AGI_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_DEF);
            case proto.ModelConst.Type.DEF_MAGIC:
                value = 2 * (2 * this.get(proto.ModelConst.Type.WIS) + this.get(proto.ModelConst.Type.ILT))
                if (this.bag) {
                    value += this.bag.get(Item.EQP_DEF_MAGIC)
                }
                return this.getPowerValue(value, this.def_magic, Define.POWER_DEF_MAGIC, Define.POWER_DEF_MAGIC_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_DEF);
            case proto.ModelConst.Type.DODGE:
                value = 10 * (this.get(proto.ModelConst.Type.AGI) + this.get(proto.ModelConst.Type.WIS)) / 25;
                return this.getPowerValue(value, this.dodge, Define.POWER_DODGE, Define.POWER_DODGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.HIT_RATE:
                if (this.bag) {
                    value = this.bag.getForPos(Item.EQP_HITRATE, PlayerBag.WEAPON_LEFT_POS)
                    value = Math.max(value, 0)
                    let rightHit = this.bag.getForPos(Item.EQP_HITRATE, PlayerBag.WEAPON_RIGHT_POS)
                    if (rightHit > 0) {
                        if (value > 0) {
                            value = Math.min(value, rightHit)
                        } else {
                            value = rightHit
                        }
                    }
                    value += GUtil.intNum(this.get(proto.ModelConst.Type.AGI) / 5);
                }
                return this.getPowerValue(value, this.hitrate, Define.POWER_HITRATE, Define.POWER_HITRATE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.HIT_MAGIC:
                return this.getPowerValue(100, this.hitMagic, Define.POWER_MAGIC_HITRATE, Define.POWER_MAGIC_HITRATE_PERCENT, Define.SKILL_TYPE_PASSIVE, proto.ModelConst.Type.MIN_HIT_MAGIC, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.CRITICAL:
                return this.getPowerValue(0, this.critical, Define.POWER_CRITICAL, Define.POWER_CRITICAL_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.FORCE_HIT:
                return this.getPowerValue(proto.ModelConst.Type.MIN_FORCE_HITRATE, this.forceHit, Define.POWER_HIT_FORCE, Define.POWER_HIT_FORCE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_FORCE_RATE);
            case proto.ModelConst.Type.EXP_UP:
                return this.exp_up;
            case proto.ModelConst.Type.WIL:
                value = (3 * this.get(proto.ModelConst.Type.WIS) + this.get(proto.ModelConst.Type.ILT)) / 10;
                return this.getPowerValue(value, this.wil, Define.POWER_WIL, Define.POWER_WIL_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.TOUGH:
                value = (2 * this.get(proto.ModelConst.Type.CON) + this.get(proto.ModelConst.Type.STR)) / 10;
                return this.getPowerValue(value, this.tough, Define.POWER_TOUGH, Define.POWER_TOUGH_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.BLOCK:
                value = this.get(proto.ModelConst.Type.AGI) / 5;
                return this.getPowerValue(value, this.block, Define.POWER_BLOCK, Define.POWER_BLOCK_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.BRK_ARMOR:
                value = this.get(proto.ModelConst.Type.AGI) + 3 * this.get(proto.ModelConst.Type.STR);
                return this.getPowerValue(value, this.brkArmor, Define.POWER_PENETRATION, Define.POWER_PENETRATION_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.MAGIC_PENETRATION:
                value = this.get(proto.ModelConst.Type.WIS) + 2 * this.get(proto.ModelConst.Type.ILT);
                return this.getPowerValue(value, this.magic_penetration, Define.POWER_MAGIC_PENETRATION, Define.POWER_MAGIC_PENETRATION_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.INSIGHT:
                value = (2 * this.get(proto.ModelConst.Type.WIS) + this.get(proto.ModelConst.Type.ILT)) / 10;
                value = GUtil.intNum(value);
                value = this.getPowerValue(value, this.insight, Define.POWER_INSIGHT, Define.POWER_INSIGHT_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
                break;
            case proto.ModelConst.Type.DEF_FIELD:
                value = 2 * this.get(proto.ModelConst.Type.ILT) + 3 * this.get(proto.ModelConst.Type.WIS);
                return this.getPowerValue(value, this.def_field, Define.POWER_DEF_FIELD, Define.POWER_DEF_FIELD_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.BACK:
                return this.getPowerValue(0, this.back, Define.POWER_BACK, Define.POWER_BACK_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.MAGIC_BACK:
                return this.getPowerValue(0, this.magic_back, Define.POWER_MAGIC_BACK, Define.POWER_MAGIC_BACK_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.LIFE_ABSORPTION:
                return this.getPowerValue(0, this.life_absorption, Define.POWER_LIFE_ABSORPTION, Define.POWER_LIFE_ABSORPTION_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_PROBABILITY);
            case proto.ModelConst.Type.MANA_ABSORPTION:
                return this.getPowerValue(0, this.mana_absorption, Define.POWER_MANA_ABSORPTION, Define.POWER_MANA_ABSORPTION_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_PROBABILITY);
            case proto.ModelConst.Type.HEAL_RECOVERY:
                return this.getPowerValue(0, this.heal_recovery, Define.POWER_HEAL_RECOVERY, Define.POWER_HEAL_RECOVERY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.MANA_RECOVERY:
                return this.getPowerValue(0, this.mana_recovery, Define.POWER_MANA_RECOVERY, Define.POWER_MANA_RECOVERY_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_BACK:
                return this.getPowerValue(this.ignore_back, 0, Define.POWER_IGNORE_BACK, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_MAGIC_BACK:
                return this.getPowerValue(this.ignore_magic_back, 0, Define.POWER_IGNORE_MAGIC_BACK, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_BLOCK:
                return this.getPowerValue(this.ignore_block, 0, Define.POWER_IGNORE_BLOCK, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_INSIGHT:
                return this.getPowerValue(this.ignore_insight, 0, Define.POWER_IGNORE_INSIGHT, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_WIL:
                return this.getPowerValue(this.ignore_wil, 0, Define.POWER_IGNORE_WIL, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_TOUCH:
                return this.getPowerValue(this.ignore_touch, 0, Define.POWER_IGNORE_TOUCH, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_BASE_ATTRIBUTE);
            case proto.ModelConst.Type.IGNORE_CRITICAL:
                return this.getPowerValue(this.ignoreCritical, 0, Define.POWER_IGNORE_CRITICAL, Define.POWER_IGNORE_CRITICAL_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.KEEPOUT_ATK_TIME:
                return this.getPowerValue(0, this.keepout_atk_time, Define.POWER_KEEPOUT_ATK_TIME, 0, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_KEEPOUT_ATK_TIME);
            case proto.ModelConst.Type.CRITICAL_DAMAGE:
                return this.getPowerValue(0, this.criticalDmg, Define.POWER_CRITICAL_DAMAGE, Define.POWER_CRITICAL_DAMAGE_PERCENT, Define.SKILL_TYPE_PASSIVE, 0, proto.ModelConst.Type.MAX_OTHER_ATTRIBUTE);
            case proto.ModelConst.Type.RECOVERY:
                return this.recovery | 0
            case proto.ModelConst.Type.ARGO:
                return this.argo | 0
            case proto.ModelConst.Type.BACK_MAX:
                return 70
            case Define.SKYARENA:
                return this.skyarenaPoint | 0
            case Define.ARENA:
                return this.arenaPoint | 0
            case Define.ARENA:
                return this.arenaPoint | 0
            case Define.SCORE_TOWER:
                return this.towerPoint | 0
            case Define.RICHMAN_SCORE:
                return this._richManPoint | 0
            case Define.TYPE_DEFEND_SCORE:
                return this.plaPoint | 0
            case Define.TYPE_CAMPWAR_SCORE:
                return this.campWarPoint | 0
            default:
                var I = void 0,
                    m = SpecialIconUtil.getSpecialIconData(type);
                m && (value = m.grade, I = this.getAttribute(type));
                value = null != I ? I : super.get(type);
        }
        return GUtil.intNum(value);
    }

    public getAttribute(e) {
        return this._obj ? this._obj[e] : null;
    }

    public setAttribute(e, t) {
        this._obj || (this._obj = {});
        this._obj[e] = t;
    }

    public getBaseValue(base: number, powerType: number, powerPercentType: number, skillType: number, min: number, max: number) {
        let val = 0 | base
        let percent = 0
        // 技能增加的固定基础
        val += this.getSkillPowerValue(skillType, powerType);
        // 技能增加的百分比
        percent = this.getSkillPowerValue(skillType, powerPercentType);
        // 装备增加的百分比
        percent += this.getBagEquipPowerValue(powerPercentType);
        //
        val += this.getPowerValueByBuffer(powerType);
        percent += this.getPowerValueByBuffer(powerPercentType);
        var u = this.formationSkill;
        null != u && (val += u.getPowerValue(powerType), percent += u.getPowerValue(powerPercentType));
        var _ = this.playerTurnMonster;
        null != _ && (val += _.getPowerValue(powerType), percent += _.getPowerValue(powerPercentType));
        val += GUtil.intNum(val * percent / 100);
        val += this.getBagEquipPowerValue(powerType);
        val = Tool.sumValue(val, 0, min, max);
        return 0 | val;
    }

    public getSkillPowerValue(skillType: number, powerType: number) {
        if (0 >= powerType) return 0
        let val = this.getPower(powerType);
        if (null == this.skillList || 0 == this.skillList.length) return val
        for (let i = 0; i < this.skillList.length; i++) {
            const skill = this.skillList[i]
            if (null != skill && skill.type == skillType) {
                val += skill.getPowerValue(powerType)
            }
        }
        return 0 | val;
    }

    public addWeaponSkillDamageOrDur(value: number, pos: number) {
        if (0 == value) return 0
        if (!this.bag) return value
        const item = this.bag.getItem(pos)
        if (!item) return value
        let percent = 0
        switch (item.type) {
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HEAVY:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_HEAVY:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HEAVY_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_HEAVY_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_STAFF:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_STAFF_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_STAFF_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_BALL:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BALL_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BALL_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_LANCE:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_LANCE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_LANCE_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_BOW:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_ARROW_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_ARROW_DAMAGE_PERCENT);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BOW_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BOW_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_GUN:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_GUN:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_GUN_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_GUN_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HAMMER:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_HAMMER:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HAMMER_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_HAMMER_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_FAN:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_FAN_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_FAN_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_SWORD:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_L_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_SWORD_L_DAMAGE_PERCENT, true);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_SWORD_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_SWORD:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_H_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_SWORD_H_DAMAGE_PERCENT);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_SWORD_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_BLADE:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_L_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BLADE_L_DAMAGE_PERCENT);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BLADE_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_BLADE:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_H_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BLADE_H_DAMAGE_PERCENT);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BLADE_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_CROSSBOW:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_CROSSBOW_H_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_CROSSBOW_H_DAMAGE_PERCENT);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BOW_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BOW_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_CROSSBOW:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_CROSSBOW_L_DAMAGE_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_CROSSBOW_L_DAMAGE_PERCENT);
                percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BOW_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_BOW_PERCENT);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HAND:
                percent = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HAND_ITEM_PERCENT);
                percent += this.getBagEquipPowerValue(Define.POWER_HAND_ITEM_PERCENT);
                break;
            default:
                percent = 0;
        }
        percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_ALL_PERCENT);
        percent += this.getBagEquipPowerValue(Define.POWER_ALL_PERCENT);
        return 0 == percent ? value : value + GUtil.intNum(value * percent / 100);
    }

    public addWeaponSkillAtkTime(value: number, weaponType: number) {
        let add = 0;
        switch (weaponType) {
            case Define.ITEM_TYPE_WEAPON_ONEHAND_SWORD:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_L_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_SWORD_L_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_SWORD_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_SWORD:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_H_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_SWORD_H_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_SWORD_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_SWORD_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_BLADE:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_L_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BLADE_L_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BLADE_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_BLADE:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_H_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BLADE_H_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BLADE_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BLADE_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HEAVY:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_HEAVY:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HEAVY_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_HEAVY_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_STAFF:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_STAFF_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_STAFF_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_BALL:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BALL_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BALL_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_LANCE:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_LANCE_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_LANCE_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_CROSSBOW:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_CROSSBOW_L_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_CROSSBOW_L_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BOW_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BOW_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_CROSSBOW:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_CROSSBOW_H_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_CROSSBOW_H_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BOW_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BOW_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_BOW:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_ARROW_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_ARROW_ATK_TIME, true);
                add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_BOW_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_BOW_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HAND:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HAND_ITEM_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_HAND_ITEM_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_GUN:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_GUN:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_GUN_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_GUN_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HAMMER:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_HAMMER:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HAMMER_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_HAMMER_ATK_TIME, true);
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_FAN:
                add = this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_FAN_ATK_TIME);
                add += this.getBagEquipPowerValue(Define.POWER_FAN_ATK_TIME, true);
                break;
            default:
                add = 0;
        }
        add += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_ALL_ATK_TIME);
        add += this.getBagEquipPowerValue(Define.POWER_ALL_ATK_TIME, true);
        return value + add;
    }

    /**
     * 获取当前装备的武器类型 没有就返回Define.BACK_ERROR_NULL_HAND
     * @returns
     */
    public getEquipWeaponType() {
        if (null == this.bag) return Define.BACK_ERROR_NULL_HAND;
        for (var e = [PlayerBag.WEAPON_LEFT_POS, PlayerBag.WEAPON_RIGHT_POS], t = 0; t < e.length; t++) {
            var i = this.bag.getItem(e[t]);
            if (null != i && !(i.durability <= 0) && i.type != Define.ITEM_TYPE_WEAPON_ONEHAND_HAND) return i.type;
        }
        return Define.BACK_ERROR_NULL_HAND;
    }

    public getAttackType(skill: Skill) {
        if (skill && skill != Skill.DUMMY_SKILL) return skill.get(Skill.SKILL_ATK_TYPE)
        return this.get(proto.ModelConst.Type.ATK_TYPE)
    }

    public getBagEquipPowerValue(powerType: number, calculateBuffer = false) {
        if (null == this.bag) return 0;
        let val = this.bag.getEquipPowerValue(powerType);
        if (null != this.itemSetData) {
            for (let i = 0; i < this.itemSetData.length;) {
                let bit = this.itemSetData[i++];
                let typ = this.itemSetData[i++];
                let value = this.itemSetData[i++];
                if (typ == powerType) {
                    let itemSetId = PlayerVo.getItemSetID(bit)
                    let itemSetNum = PlayerVo.getItemSetNum(bit)
                    if (this.bag.getEquipItemSetNum(itemSetId) >= itemSetNum) {
                        val += value
                    }
                }
            }
        }
        if (calculateBuffer) {
            val += this.getPowerValueByBuffer(powerType)
        }
        return 0 | val;
    }

    // 图鉴属性
    public getPower(e) {
        var t = 0;
        if (this.type == MODEL_TYPE.TYPE_PLAYER) {
            t += 0 | RoleGodData.getPower(this.isMyPlayer, e)
            t += 0 | BookGroupModel.getBookGroupPower(this.isMyPlayer, e)
        }
        return t;
    }

    public getPowerValue(base: number, add: number, powerType: number, powerPercentType: number, skillType: number, min: number, max: number, weapon = false) {
        let val = GUtil.intNum(base)
        // 技能
        val += this.getSkillPowerValue(skillType, powerType)
        // 技能百分比
        let percent = this.getSkillPowerValue(skillType, powerPercentType)
        // buff和其他
        val += this.getPowerValueByBuffer(powerType)
        // buff百分比
        percent += this.getPowerValueByBuffer(powerPercentType)
        if (null != this.bag) {
            val += this.getBagEquipPowerValue(powerType)
            percent += this.getBagEquipPowerValue(powerPercentType)
        }
        if (null != this.formationSkill) {
            val += this.formationSkill.getPowerValue(powerType)
            percent += this.formationSkill.getPowerValue(powerPercentType)
        }
        let turn = this.playerTurnMonster;
        if (null != turn) {
            val += turn.getPowerValue(powerType)
            percent += turn.getPowerValue(powerPercentType)
        }
        if (weapon && this.getEquipWeaponType() == Define.BACK_ERROR_NULL_HAND) {
            if (null != this.bag) {
                percent += this.getBagEquipPowerValue(Define.POWER_HAND_PERCENT)
            }
            percent += this.getSkillPowerValue(Define.SKILL_TYPE_PASSIVE, Define.POWER_HAND_PERCENT)
        }
        val += GUtil.intNum(val * percent / 100);
        val = Tool.sumValue(val, add, min, max);
        return GUtil.intNum(val);
    }

    /**
     * 计算 药水 + 称号 + 国家 + 战斗属性
     * @param powerType
     * @returns
     */
    public getPowerValueByBuffer(powerType: number) {
        let val = 0;
        this.power == powerType && (val += this.powerValue)
        this.titlePower1 == powerType && (val += this.titlePowerValue1)
        this.titlePower2 == powerType && (val += this.titlePowerValue2)
        if (this.fightPowerList) {
            for (let i = 0; i < this.fightPowerList.length; i++) {
                var o = this.fightPowerList[i];
                o[0] == powerType && (val += o[1]);
            }
        }
        return val += this.getCountryBuffer(powerType);
    }

    public updateAndRefreshIcon() {
        this.clearMemberHideStatus();
        this.updateIcon();
        this.refreshWorldSprite();
    }

    /**
     * 设置Long类型数值的特定位段
     * @param target 目标数值,将被修改的Long值
     * @param source 源数值,提供位值的Long值
     * @param offsetConst 位偏移量常量
     * @param lenConst 位长度常量
     * @param offsetBits 额外的位偏移量,用于低位设置
     * @param lengthMask 位长度掩码,用于低位设置
     * @param setHigh 是否设置高32位,默认设置低32位
     * @returns 修改后的targetValue
     */
    public static setIconValue(target: Long, source: Long, offsetConst: number, lenConst: number, offsetBits: number, lengthMask: number, setHigh?: boolean) {
        // 根据setHigh获取源数值的高位或低位部分
        let sourceSegment = setHigh ? source.high : source.low
        // 从源数值中提取指定位段
        let extractedBits = sourceSegment >> offsetConst & lenConst
        if (extractedBits <= 0) return target
        // 设置高位或低位的指定位段
        if (setHigh) {
            target.high &= ~(lenConst << offsetConst)
            target.high |= (extractedBits & lenConst) << offsetConst
        } else {
            target.low &= ~(lenConst << offsetConst)
            target.low |= (extractedBits & lenConst) << offsetConst
        }
        // 设置额外的低位部分
        if (offsetBits >= 0) {
            extractedBits = source.low >> offsetBits & lengthMask
            target.low &= ~(lengthMask << offsetBits)
            target.low |= (extractedBits & lengthMask) << offsetBits
        }
        return target
    }

    public getIllusionItem(e, t) {
        if (t.id != e) {
            var i = this.putIllusion[e];
            return 0 == i ? void (t.itemInfo = null) : t.itemInfo ? t.itemInfo : void 0;
        }
    }

    public updateIconByFashion() {
        // 去掉时装数据
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_FASH_ADD << proto.ModelConst.Type.OFFSET_FASH_ADD)
        if (!this.bag) return
        // 穿戴的时装
        let fashionEquip = this.bag.getItem(PlayerBag.ARMOR_FASHION_POS);
        if (!fashionEquip) return
        // 幻化
        if (fashionEquip.illusionItemId) {
            const equip = this.getIllusionItem(fashionEquip.illusionItemId, fashionEquip);
            if (equip) {
                fashionEquip = equip
            }
        }

        // 过期了
        if (fashionEquip.isTimeItem() && fashionEquip.isExpired()) return

        let fashIcon1 = fashionEquip.fashIcon1
        let fashIcon2 = fashionEquip.fashIcon2
        let fashIcon3 = fashionEquip.fashIcon3

        if (fashIcon1 && !fashIcon1.isZero()) {
            // 头发
            let hairStyle = fashIcon1.low >> proto.ModelConst.Type.OFFSET_HAIR_STYLE & proto.ModelConst.Type.LEN_HAIR_STYLE
            let hairAdd = fashIcon1.high >> proto.ModelConst.Type.OFFSET_HAIR_ADD & proto.ModelConst.Type.LEN_HAIR_ADD
            if (hairStyle > 0 || hairAdd > 0) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAIR_STYLE << proto.ModelConst.Type.OFFSET_HAIR_STYLE)
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAIR_COLOR << proto.ModelConst.Type.OFFSET_HAIR_COLOR)
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_HAIR_ADD << proto.ModelConst.Type.OFFSET_HAIR_ADD)
            }
            // 脸
            let faceStyle = fashIcon1.low >> proto.ModelConst.Type.OFFSET_FACE_STYLE & proto.ModelConst.Type.LEN_FACE_STYLE
            let faceAdd = fashIcon1.high >> proto.ModelConst.Type.OFFSET_FACE_ADD & proto.ModelConst.Type.LEN_FACE_ADD;
            if (faceStyle > 0 || faceAdd > 0) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_FACE_STYLE << proto.ModelConst.Type.OFFSET_FACE_STYLE)
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_FACE_ADD << proto.ModelConst.Type.OFFSET_FACE_ADD)
            }
            // 手
            let handStyle = fashIcon1.low >> proto.ModelConst.Type.OFFSET_HAND_STYLE & proto.ModelConst.Type.LEN_HAND_STYLE
            let handAdd = fashIcon1.high >> proto.ModelConst.Type.OFFSET_HAND_ADD & proto.ModelConst.Type.LEN_HAND_ADD;
            if (handStyle > 0 || handAdd > 0) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAND_STYLE << proto.ModelConst.Type.OFFSET_HAND_STYLE)
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAND_COLOR << proto.ModelConst.Type.OFFSET_HAND_COLOR)
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_HAND_ADD << proto.ModelConst.Type.OFFSET_HAND_ADD)
            }
            // 脚
            let feetStyle = fashIcon1.low >> proto.ModelConst.Type.OFFSET_FEET_STYLE & proto.ModelConst.Type.LEN_FEET_STYLE
            let feetAdd = fashIcon1.high >> proto.ModelConst.Type.OFFSET_FEET_ADD & proto.ModelConst.Type.LEN_FEET_ADD;
            if (feetStyle > 0 || feetAdd > 0) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_FEET_STYLE << proto.ModelConst.Type.OFFSET_FEET_STYLE)
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_FEET_COLOR << proto.ModelConst.Type.OFFSET_FEET_COLOR)
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_FEET_ADD << proto.ModelConst.Type.OFFSET_FEET_ADD)
            }
            // 头盔
            let helmetStyle = fashIcon1.low >> proto.ModelConst.Type.OFFSET_HELMET_STYLE & proto.ModelConst.Type.LEN_HELMET_STYLE
            let helmetAdd = fashIcon1.high >> proto.ModelConst.Type.OFFSET_HELMET_ADD & proto.ModelConst.Type.LEN_HELMET_ADD;
            if (helmetStyle > 0 || helmetAdd > 0) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HELMET_STYLE << proto.ModelConst.Type.OFFSET_HELMET_STYLE)
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HELMET_COLOR << proto.ModelConst.Type.OFFSET_HELMET_COLOR)
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_HELMET_ADD << proto.ModelConst.Type.OFFSET_HELMET_ADD)
            }
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_HAIR_STYLE, proto.ModelConst.Type.LEN_HAIR_STYLE, proto.ModelConst.Type.OFFSET_HAIR_COLOR, proto.ModelConst.Type.LEN_HAIR_COLOR);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_HAIR_ADD, proto.ModelConst.Type.LEN_HAIR_ADD, proto.ModelConst.Type.OFFSET_HAIR_COLOR, proto.ModelConst.Type.LEN_HAIR_COLOR, true);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_FACE_STYLE, proto.ModelConst.Type.LEN_FACE_STYLE, -1, -1);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_FACE_ADD, proto.ModelConst.Type.LEN_FACE_ADD, -1, -1, true);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_HAND_STYLE, proto.ModelConst.Type.LEN_HAND_STYLE, proto.ModelConst.Type.OFFSET_HAND_COLOR, proto.ModelConst.Type.LEN_HAND_COLOR);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_HAND_ADD, proto.ModelConst.Type.LEN_HAND_ADD, proto.ModelConst.Type.OFFSET_HAND_COLOR, proto.ModelConst.Type.LEN_HAND_COLOR, true);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_FEET_STYLE, proto.ModelConst.Type.LEN_FEET_STYLE, proto.ModelConst.Type.OFFSET_FEET_COLOR, proto.ModelConst.Type.LEN_FEET_COLOR);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_FEET_ADD, proto.ModelConst.Type.LEN_FEET_ADD, proto.ModelConst.Type.OFFSET_FEET_COLOR, proto.ModelConst.Type.LEN_FEET_COLOR, true);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_HELMET_STYLE, proto.ModelConst.Type.LEN_HELMET_STYLE, proto.ModelConst.Type.OFFSET_HELMET_COLOR, proto.ModelConst.Type.LEN_HELMET_COLOR);
            this.icon1 = PlayerVo.setIconValue(this.icon1, fashIcon1, proto.ModelConst.Type.OFFSET_HELMET_ADD, proto.ModelConst.Type.LEN_HELMET_ADD, proto.ModelConst.Type.OFFSET_HELMET_COLOR, proto.ModelConst.Type.LEN_HELMET_COLOR, true);
        }

        if (fashIcon2 && !fashIcon2.isZero()) {
            // 肩膀
            let shoulderStyle = fashIcon2.low >> proto.ModelConst.Type.OFFSET_SHOULDER_STYLE & proto.ModelConst.Type.LEN_SHOULDER_STYLE
            let shoulderAdd = fashIcon2.low >> proto.ModelConst.Type.OFFSET_SHOULDER_ADD & proto.ModelConst.Type.LEN_SHOULDER_ADD
            let shoulderAdd2 = fashIcon2.high >> proto.ModelConst.Type.OFFSET_SHOULDER_ADD_2 & proto.ModelConst.Type.LEN_SHOULDER_ADD_2;
            if (shoulderStyle > 0 || shoulderAdd > 0 || shoulderAdd2 > 0) {
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_SHOULDER_STYLE << proto.ModelConst.Type.OFFSET_SHOULDER_STYLE)
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_SHOULDER_COLOR << proto.ModelConst.Type.OFFSET_SHOULDER_COLOR)
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_SHOULDER_ADD << proto.ModelConst.Type.OFFSET_SHOULDER_ADD)
                this.icon2.high &= ~(proto.ModelConst.Type.LEN_SHOULDER_ADD_2 << proto.ModelConst.Type.OFFSET_SHOULDER_ADD_2)
            }
            // 武器
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_SHOULDER_STYLE, proto.ModelConst.Type.LEN_SHOULDER_STYLE, proto.ModelConst.Type.OFFSET_SHOULDER_COLOR, proto.ModelConst.Type.LEN_SHOULDER_COLOR);
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_SHOULDER_ADD, proto.ModelConst.Type.LEN_SHOULDER_ADD, -1, -1);
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_SHOULDER_ADD_2, proto.ModelConst.Type.LEN_SHOULDER_ADD_2, -1, -1, true);
            let leftWeaponStyle = fashIcon2.low >> proto.ModelConst.Type.OFFSET_LWEAPON_STYLE & proto.ModelConst.Type.LEN_LWEAPON_STYLE
            let leftWeaponAdd = fashIcon2.high >> proto.ModelConst.Type.OFFSET_LWEAPON_ADD & proto.ModelConst.Type.LEN_LWEAPON_ADD
            let rightWeaponStyle = fashIcon2.low >> proto.ModelConst.Type.OFFSET_RWEAPON_STYLE & proto.ModelConst.Type.LEN_RWEAPON_STYLE
            let rightWeaponAdd = fashIcon2.high >> proto.ModelConst.Type.OFFSET_RWEAPON_ADD & proto.ModelConst.Type.LEN_RWEAPON_ADD;
            if (leftWeaponStyle > 0 || rightWeaponStyle > 0 || leftWeaponAdd > 0 || rightWeaponAdd > 0) {
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_LWEAPON_STYLE << proto.ModelConst.Type.OFFSET_LWEAPON_STYLE)
                this.icon2.high &= ~(proto.ModelConst.Type.LEN_LWEAPON_ADD << proto.ModelConst.Type.OFFSET_LWEAPON_ADD)
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_LWEAPON_COLOR << proto.ModelConst.Type.OFFSET_LWEAPON_COLOR)
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_RWEAPON_STYLE << proto.ModelConst.Type.OFFSET_RWEAPON_STYLE)
                this.icon2.high &= ~(proto.ModelConst.Type.LEN_RWEAPON_ADD << proto.ModelConst.Type.OFFSET_RWEAPON_ADD)
                this.icon2.low &= ~(proto.ModelConst.Type.LEN_RWEAPON_COLOR << proto.ModelConst.Type.OFFSET_RWEAPON_COLOR)
            }
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_LWEAPON_STYLE, proto.ModelConst.Type.LEN_LWEAPON_STYLE, proto.ModelConst.Type.OFFSET_LWEAPON_COLOR, proto.ModelConst.Type.LEN_LWEAPON_COLOR);
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_LWEAPON_ADD, proto.ModelConst.Type.LEN_LWEAPON_ADD, proto.ModelConst.Type.OFFSET_LWEAPON_COLOR, proto.ModelConst.Type.LEN_LWEAPON_COLOR, true);
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_RWEAPON_STYLE, proto.ModelConst.Type.LEN_RWEAPON_STYLE, proto.ModelConst.Type.OFFSET_RWEAPON_COLOR, proto.ModelConst.Type.LEN_RWEAPON_COLOR);
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_RWEAPON_ADD, proto.ModelConst.Type.LEN_RWEAPON_ADD, proto.ModelConst.Type.OFFSET_RWEAPON_COLOR, proto.ModelConst.Type.LEN_RWEAPON_COLOR, true);
            this.icon2 = PlayerVo.setIconValue(this.icon2, fashIcon2, proto.ModelConst.Type.OFFSET_WEAPON_FLASH, proto.ModelConst.Type.LEN_WEAPON_FLASH, -1, -1);
        }

        if (fashIcon3 && !fashIcon3.isZero()) {
            // 翅膀
            let backStyle = fashIcon3.low >> proto.ModelConst.Type.OFFSET_BACK_STYLE & proto.ModelConst.Type.LEN_BACK_STYLE
            let backAdd = fashIcon3.low >> proto.ModelConst.Type.OFFSET_BACK_ADD & proto.ModelConst.Type.LEN_BACK_ADD
            let backAdd2 = fashIcon3.high >> proto.ModelConst.Type.OFFSET_BACK_ADD_2 & proto.ModelConst.Type.LEN_BACK_ADD_2;
            if (backStyle > 0 || backAdd > 0 || backAdd2 > 0) {
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_BACK_STYLE << proto.ModelConst.Type.OFFSET_BACK_STYLE)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_BACK_COLOR << proto.ModelConst.Type.OFFSET_BACK_COLOR)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_BACK_ADD << proto.ModelConst.Type.OFFSET_BACK_ADD)
                this.icon3.high &= ~(proto.ModelConst.Type.LEN_BACK_ADD_2 << proto.ModelConst.Type.OFFSET_BACK_ADD_2)
            }
            // 衣服
            let clothesStyle = fashIcon3.low >> proto.ModelConst.Type.OFFSET_CLOTHES_STYLE & proto.ModelConst.Type.LEN_CLOTHES_STYLE
            let clothesAdd = fashIcon3.high >> proto.ModelConst.Type.OFFSET_CLOTHES_ADD & proto.ModelConst.Type.LEN_CLOTHES_ADD;
            if (clothesStyle > 0 || clothesAdd > 0) {
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_CLOTHES_STYLE << proto.ModelConst.Type.OFFSET_CLOTHES_STYLE)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_CLOTHES_COLOR << proto.ModelConst.Type.OFFSET_CLOTHES_COLOR)
                this.icon3.high &= ~(proto.ModelConst.Type.LEN_CLOTHES_ADD << proto.ModelConst.Type.OFFSET_CLOTHES_ADD)
            }
            // 裤子
            let trousersStyle = fashIcon3.low >> proto.ModelConst.Type.OFFSET_TROUSERS_STYLE & proto.ModelConst.Type.LEN_TROUSERS_STYLE
            let trousersAdd = fashIcon3.high >> proto.ModelConst.Type.OFFSET_TROUSERS_ADD & proto.ModelConst.Type.LEN_TROUSERS_ADD;
            if (trousersStyle > 0 || trousersAdd > 0) {
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_TROUSERS_STYLE << proto.ModelConst.Type.OFFSET_TROUSERS_STYLE)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_TROUSERS_COLOR << proto.ModelConst.Type.OFFSET_TROUSERS_COLOR)
                this.icon3.high &= ~(proto.ModelConst.Type.LEN_TROUSERS_ADD << proto.ModelConst.Type.OFFSET_TROUSERS_ADD)
            }
            // 坐骑
            let transportStyle = fashIcon3.low >> proto.ModelConst.Type.OFFSET_TRANSPORT_STYLE & proto.ModelConst.Type.LEN_TRANSPORT_STYLE
            let transportAdd = fashIcon3.low >> proto.ModelConst.Type.OFFSET_TRANSPORT_ADD & proto.ModelConst.Type.LEN_TRANSPORT_ADD
            let transportAdd2 = fashIcon3.low >> proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_2 & proto.ModelConst.Type.LEN_TRANSPORT_ADD_2
            let transportAdd3 = fashIcon3.high >> proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_3 & proto.ModelConst.Type.LEN_TRANSPORT_ADD_3;
            if (transportStyle > 0 || transportAdd > 0 || transportAdd2 > 0 || transportAdd3 > 0) {
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_STYLE << proto.ModelConst.Type.OFFSET_TRANSPORT_STYLE)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_COLOR << proto.ModelConst.Type.OFFSET_TRANSPORT_COLOR)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_ADD << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD)
                this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_ADD_2 << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_2)
                this.icon3.high &= ~(proto.ModelConst.Type.LEN_TRANSPORT_ADD_3 << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_3)
            }
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_BACK_STYLE, proto.ModelConst.Type.LEN_BACK_STYLE, proto.ModelConst.Type.OFFSET_BACK_COLOR, proto.ModelConst.Type.LEN_BACK_COLOR);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_BACK_ADD, proto.ModelConst.Type.LEN_BACK_ADD, -1, -1);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_BACK_ADD_2, proto.ModelConst.Type.LEN_BACK_ADD_2, -1, -1, true);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_CLOTHES_STYLE, proto.ModelConst.Type.LEN_CLOTHES_STYLE, proto.ModelConst.Type.OFFSET_CLOTHES_COLOR, proto.ModelConst.Type.LEN_CLOTHES_COLOR);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_CLOTHES_ADD, proto.ModelConst.Type.LEN_CLOTHES_ADD, proto.ModelConst.Type.OFFSET_CLOTHES_COLOR, proto.ModelConst.Type.LEN_CLOTHES_COLOR, true);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_TROUSERS_STYLE, proto.ModelConst.Type.LEN_TROUSERS_STYLE, proto.ModelConst.Type.OFFSET_TROUSERS_COLOR, proto.ModelConst.Type.LEN_TROUSERS_COLOR);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_TROUSERS_ADD, proto.ModelConst.Type.LEN_TROUSERS_ADD, proto.ModelConst.Type.OFFSET_TROUSERS_COLOR, proto.ModelConst.Type.LEN_TROUSERS_COLOR, true);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_TRANSPORT_STYLE, proto.ModelConst.Type.LEN_TRANSPORT_STYLE, proto.ModelConst.Type.OFFSET_TRANSPORT_COLOR, proto.ModelConst.Type.LEN_TRANSPORT_COLOR);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_TRANSPORT_ADD, proto.ModelConst.Type.LEN_TRANSPORT_ADD, -1, -1);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_2, proto.ModelConst.Type.LEN_TRANSPORT_ADD_2, -1, -1);
            this.icon3 = PlayerVo.setIconValue(this.icon3, fashIcon3, proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_3, proto.ModelConst.Type.LEN_TRANSPORT_ADD_3, -1, -1, true);
        }
        const isFashIcon1Valid = fashIcon1 && !fashIcon1.isZero()
        const isFashIcon2Valid = fashIcon2 && !fashIcon2.isZero()
        const isFashIcon3Valid = fashIcon3 && !fashIcon3.isZero()

        if (isFashIcon1Valid || isFashIcon2Valid || isFashIcon3Valid) {
            this.icon3.low |= (1 & proto.ModelConst.Type.LEN_FASH_ADD) << proto.ModelConst.Type.OFFSET_FASH_ADD
        }
    }

    public updateHair(e) {
        if (null != e) {
            var t = e.icon;
            if (!(0 >= t)) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAIR_STYLE << proto.ModelConst.Type.OFFSET_HAIR_STYLE);
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAIR_COLOR << proto.ModelConst.Type.OFFSET_HAIR_COLOR);
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_HAIR_ADD << proto.ModelConst.Type.OFFSET_HAIR_ADD);
                var i = t - proto.ModelConst.Type.START_HAIR;
                if (i > 0) {
                    var o = 1 + ((i - 1) / 8 | 0),
                        n = (i - 1) % 8;
                    this.icon1.low |= (o & proto.ModelConst.Type.LEN_HAIR_STYLE) << proto.ModelConst.Type.OFFSET_HAIR_STYLE;
                    this.icon1.low |= (n & proto.ModelConst.Type.LEN_HAIR_COLOR) << proto.ModelConst.Type.OFFSET_HAIR_COLOR;
                    var r = o >> Tool.getBitNum(proto.ModelConst.Type.LEN_HAIR_STYLE);
                    this.icon1.high |= (r & proto.ModelConst.Type.LEN_HAIR_ADD) << proto.ModelConst.Type.OFFSET_HAIR_ADD;
                }
            }
        }
    }

    public updateFace(e) {
        if (null != e) {
            var t = e.icon;
            if (!(0 >= t)) {
                this.icon1.low &= ~(proto.ModelConst.Type.LEN_FACE_STYLE << proto.ModelConst.Type.OFFSET_FACE_STYLE);
                this.icon1.high &= ~(proto.ModelConst.Type.LEN_FACE_ADD << proto.ModelConst.Type.OFFSET_FACE_ADD);
                var i = t - proto.ModelConst.Type.START_FACE;
                if (i > 0) {
                    var o = 1 + ((i - 1) / 8 | 0);
                    this.icon1.low |= (o & proto.ModelConst.Type.LEN_FACE_STYLE) << proto.ModelConst.Type.OFFSET_FACE_STYLE;
                    var n = o >> Tool.getBitNum(proto.ModelConst.Type.LEN_FACE_STYLE);
                    this.icon1.high |= (n & proto.ModelConst.Type.LEN_FACE_ADD) << proto.ModelConst.Type.OFFSET_FACE_ADD;
                }
            }
        }
    }

    public updateIcon() {
        if (!this.bag) return
        let tmpEquip: Item

        // 清理样式
        this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAND_STYLE << proto.ModelConst.Type.OFFSET_HAND_STYLE)
        this.icon1.high &= ~(proto.ModelConst.Type.LEN_HAND_ADD << proto.ModelConst.Type.OFFSET_HAND_ADD)
        this.icon1.low &= ~(proto.ModelConst.Type.LEN_HAND_COLOR << proto.ModelConst.Type.OFFSET_HAND_COLOR)
        this.icon1.low &= ~(proto.ModelConst.Type.LEN_FEET_STYLE << proto.ModelConst.Type.OFFSET_FEET_STYLE)
        this.icon1.high &= ~(proto.ModelConst.Type.LEN_FEET_ADD << proto.ModelConst.Type.OFFSET_FEET_ADD)
        this.icon1.low &= ~(proto.ModelConst.Type.LEN_FEET_COLOR << proto.ModelConst.Type.OFFSET_FEET_COLOR)
        this.icon1 = ModelVo.setHandAndFeet(this.icon1)
        this.icon1.low &= ~(proto.ModelConst.Type.LEN_HELMET_STYLE << proto.ModelConst.Type.OFFSET_HELMET_STYLE)
        this.icon1.high &= ~(proto.ModelConst.Type.LEN_HELMET_ADD << proto.ModelConst.Type.OFFSET_HELMET_ADD)
        this.icon1.low &= ~(proto.ModelConst.Type.LEN_HELMET_COLOR << proto.ModelConst.Type.OFFSET_HELMET_COLOR)

        // 头盔
        tmpEquip = this.bag.getItem(PlayerBag.ARMOR_HEAD_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon - proto.ModelConst.Type.START_HELMET
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 4 | 0);
                let o = (icon - 1) % 4;
                this.icon1.low |= (i & proto.ModelConst.Type.LEN_HELMET_STYLE) << proto.ModelConst.Type.OFFSET_HELMET_STYLE;
                this.icon1.low |= (o & proto.ModelConst.Type.LEN_HELMET_COLOR) << proto.ModelConst.Type.OFFSET_HELMET_COLOR;
                let n = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_HELMET_STYLE);
                this.icon1.high |= (n & proto.ModelConst.Type.LEN_HELMET_ADD) << proto.ModelConst.Type.OFFSET_HELMET_ADD;
            }
        }

        // 肩部
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_SHOULDER_STYLE << proto.ModelConst.Type.OFFSET_SHOULDER_STYLE)
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_SHOULDER_COLOR << proto.ModelConst.Type.OFFSET_SHOULDER_COLOR)
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_SHOULDER_ADD << proto.ModelConst.Type.OFFSET_SHOULDER_ADD)
        this.icon2.high &= ~(proto.ModelConst.Type.LEN_SHOULDER_ADD_2 << proto.ModelConst.Type.OFFSET_SHOULDER_ADD_2)
        tmpEquip = this.bag.getItem(PlayerBag.ARMOR_SHOULDER_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon - proto.ModelConst.Type.START_SHOULDER
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 8 | 0);
                let o = (icon - 1) % 8;
                this.icon2.low |= (i & proto.ModelConst.Type.LEN_SHOULDER_STYLE) << proto.ModelConst.Type.OFFSET_SHOULDER_STYLE;
                this.icon2.low |= (o & proto.ModelConst.Type.LEN_SHOULDER_COLOR) << proto.ModelConst.Type.OFFSET_SHOULDER_COLOR;
                let n = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_SHOULDER_STYLE);
                this.icon2.low |= (n & proto.ModelConst.Type.LEN_SHOULDER_ADD) << proto.ModelConst.Type.OFFSET_SHOULDER_ADD;
                let r = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_SHOULDER_STYLE) + Tool.getBitNum(proto.ModelConst.Type.LEN_SHOULDER_ADD);
                this.icon2.high |= (r & proto.ModelConst.Type.LEN_SHOULDER_ADD) << proto.ModelConst.Type.OFFSET_SHOULDER_ADD;
            }
        }

        // 武器闪光
        let weaponFlashType = 0;
        // 左手武器
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_WEAPON_FLASH << proto.ModelConst.Type.OFFSET_WEAPON_FLASH);
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_LWEAPON_STYLE << proto.ModelConst.Type.OFFSET_LWEAPON_STYLE)
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_LWEAPON_COLOR << proto.ModelConst.Type.OFFSET_LWEAPON_COLOR)
        this.icon2.high &= ~(proto.ModelConst.Type.LEN_LWEAPON_ADD << proto.ModelConst.Type.OFFSET_LWEAPON_ADD)
        tmpEquip = this.bag.getItem(PlayerBag.WEAPON_LEFT_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon > 30000 ? tmpEquip.icon - proto.ModelConst.Type.START_WEAPON_ADD1 : tmpEquip.icon - proto.ModelConst.Type.START_WEAPON
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 4 | 0);
                let o = (icon - 1) % 4;
                this.icon2.low |= (i & proto.ModelConst.Type.LEN_LWEAPON_STYLE) << proto.ModelConst.Type.OFFSET_LWEAPON_STYLE;
                this.icon2.low |= (o & proto.ModelConst.Type.LEN_LWEAPON_COLOR) << proto.ModelConst.Type.OFFSET_LWEAPON_COLOR;
                let n = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_LWEAPON_STYLE);
                this.icon2.high |= (n & proto.ModelConst.Type.LEN_LWEAPON_ADD) << proto.ModelConst.Type.OFFSET_LWEAPON_ADD;
                weaponFlashType = tmpEquip.getWeaponFlashType();
            }
        }

        // 右手武器
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_RWEAPON_STYLE << proto.ModelConst.Type.OFFSET_RWEAPON_STYLE)
        this.icon2.low &= ~(proto.ModelConst.Type.LEN_RWEAPON_COLOR << proto.ModelConst.Type.OFFSET_RWEAPON_COLOR)
        this.icon2.high &= ~(proto.ModelConst.Type.LEN_RWEAPON_ADD << proto.ModelConst.Type.OFFSET_RWEAPON_ADD)
        tmpEquip = this.bag.getItem(PlayerBag.WEAPON_RIGHT_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon > 30000 ? tmpEquip.icon - proto.ModelConst.Type.START_WEAPON_ADD1 : tmpEquip.icon - proto.ModelConst.Type.START_WEAPON
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 4 | 0);
                let o = (icon - 1) % 4;
                this.icon2.low |= (i & proto.ModelConst.Type.LEN_RWEAPON_STYLE) << proto.ModelConst.Type.OFFSET_RWEAPON_STYLE;
                this.icon2.low |= (o & proto.ModelConst.Type.LEN_RWEAPON_COLOR) << proto.ModelConst.Type.OFFSET_RWEAPON_COLOR;
                let n = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_RWEAPON_STYLE);
                this.icon2.high |= (n & proto.ModelConst.Type.LEN_RWEAPON_ADD) << proto.ModelConst.Type.OFFSET_RWEAPON_ADD;
                let rightWeaponFlashType = tmpEquip.getWeaponFlashType();
                // 取高的闪光效果
                if (rightWeaponFlashType > 0 && rightWeaponFlashType > weaponFlashType) {
                    weaponFlashType = rightWeaponFlashType
                }
            }
        }
        if (weaponFlashType > 0) {
            this.icon2.low |= (weaponFlashType & proto.ModelConst.Type.LEN_WEAPON_FLASH) << proto.ModelConst.Type.OFFSET_WEAPON_FLASH
        }
        // B背部
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_BACK_STYLE << proto.ModelConst.Type.OFFSET_BACK_STYLE)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_BACK_COLOR << proto.ModelConst.Type.OFFSET_BACK_COLOR)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_BACK_ADD << proto.ModelConst.Type.OFFSET_BACK_ADD)
        this.icon3.high &= ~(proto.ModelConst.Type.LEN_BACK_ADD_2 << proto.ModelConst.Type.OFFSET_BACK_ADD_2)
        tmpEquip = this.bag.getItem(PlayerBag.ARMOR_BACK_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon - proto.ModelConst.Type.START_BACK
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 4 | 0);
                let o = (icon - 1) % 4;
                this.icon3.low |= (i & proto.ModelConst.Type.LEN_BACK_STYLE) << proto.ModelConst.Type.OFFSET_BACK_STYLE;
                this.icon3.low |= (o & proto.ModelConst.Type.LEN_BACK_COLOR) << proto.ModelConst.Type.OFFSET_BACK_COLOR;
                let c = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_BACK_STYLE);
                this.icon3.low |= (c & proto.ModelConst.Type.LEN_BACK_ADD) << proto.ModelConst.Type.OFFSET_BACK_ADD;
                let p = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_BACK_STYLE) + proto.ModelConst.Type.LEN_BACK_ADD;
                this.icon3.high |= (p & proto.ModelConst.Type.LEN_BACK_ADD_2) << proto.ModelConst.Type.OFFSET_BACK_ADD_2;
            }
        }

        // 衣服
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_CLOTHES_STYLE << proto.ModelConst.Type.OFFSET_CLOTHES_STYLE)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_CLOTHES_COLOR << proto.ModelConst.Type.OFFSET_CLOTHES_COLOR)
        this.icon3.high &= ~(proto.ModelConst.Type.LEN_CLOTHES_ADD << proto.ModelConst.Type.OFFSET_CLOTHES_ADD)
        tmpEquip = this.bag.getItem(PlayerBag.ARMOR_CLOTHES_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon - proto.ModelConst.Type.START_CLOTHES
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 8 | 0);
                let o = (icon - 1) % 8;
                this.icon3.low |= (i & proto.ModelConst.Type.LEN_CLOTHES_STYLE) << proto.ModelConst.Type.OFFSET_CLOTHES_STYLE;
                this.icon3.low |= (o & proto.ModelConst.Type.LEN_CLOTHES_COLOR) << proto.ModelConst.Type.OFFSET_CLOTHES_COLOR;
                let n = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_CLOTHES_STYLE);
                this.icon3.high |= (n & proto.ModelConst.Type.LEN_CLOTHES_ADD) << proto.ModelConst.Type.OFFSET_CLOTHES_ADD;
            }
        }
        // 裤子
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_TROUSERS_STYLE << proto.ModelConst.Type.OFFSET_TROUSERS_STYLE)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_TROUSERS_COLOR << proto.ModelConst.Type.OFFSET_TROUSERS_COLOR)
        this.icon3.high &= ~(proto.ModelConst.Type.LEN_TROUSERS_ADD << proto.ModelConst.Type.OFFSET_TROUSERS_ADD)
        tmpEquip = this.bag.getItem(PlayerBag.ARMOR_TROUSERS_POS)
        if (tmpEquip && tmpEquip.icon > 0) {
            let icon = tmpEquip.icon - proto.ModelConst.Type.START_TROUSERS
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 8 | 0);
                let o = (icon - 1) % 8;
                this.icon3.low |= (i & proto.ModelConst.Type.LEN_TROUSERS_STYLE) << proto.ModelConst.Type.OFFSET_TROUSERS_STYLE;
                this.icon3.low |= (o & proto.ModelConst.Type.LEN_TROUSERS_COLOR) << proto.ModelConst.Type.OFFSET_TROUSERS_COLOR;
                let n = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_TROUSERS_STYLE);
                this.icon3.high |= (n & proto.ModelConst.Type.LEN_TROUSERS_ADD) << proto.ModelConst.Type.OFFSET_TROUSERS_ADD;
            }
        }

        // 坐骑
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_STYLE << proto.ModelConst.Type.OFFSET_TRANSPORT_STYLE)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_COLOR << proto.ModelConst.Type.OFFSET_TRANSPORT_COLOR)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_ADD << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD)
        this.icon3.low &= ~(proto.ModelConst.Type.LEN_TRANSPORT_ADD_2 << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_2)
        this.icon3.high &= ~(proto.ModelConst.Type.LEN_TRANSPORT_ADD_3 << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_3)
        tmpEquip = this.bag.getItem(PlayerBag.ARMOR_TRANSPORT_POS)
        if (tmpEquip && tmpEquip.illusionItemId) {
            // 幻化的坐骑
            const illusionTransportEquip = this.getIllusionItem(tmpEquip.illusionItemId, tmpEquip);
            if (illusionTransportEquip) {
                tmpEquip = illusionTransportEquip
            }
        }
        if (null != tmpEquip && tmpEquip.icon > 0) {
            const expired = tmpEquip.isTimeItem() && tmpEquip.isExpired();
            let icon = 0
            if (!expired) {
                icon = tmpEquip.icon - proto.ModelConst.Type.START_TRANSPORT
            }
            if (icon > 0) {
                let i = 1 + ((icon - 1) / 4 | 0);
                let o = (icon - 1) % 4;
                this.icon3.low |= (i & proto.ModelConst.Type.LEN_TRANSPORT_STYLE) << proto.ModelConst.Type.OFFSET_TRANSPORT_STYLE;
                this.icon3.low |= (o & proto.ModelConst.Type.LEN_TRANSPORT_COLOR) << proto.ModelConst.Type.OFFSET_TRANSPORT_COLOR;
                let T = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_TRANSPORT_STYLE);
                this.icon3.low |= (T & proto.ModelConst.Type.LEN_TRANSPORT_ADD) << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD;
                let E = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_TRANSPORT_STYLE) + proto.ModelConst.Type.LEN_TRANSPORT_ADD;
                this.icon3.low |= (E & proto.ModelConst.Type.LEN_TRANSPORT_ADD_2) << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_2;
                let y = i >> Tool.getBitNum(proto.ModelConst.Type.LEN_TRANSPORT_STYLE) + proto.ModelConst.Type.LEN_TRANSPORT_ADD + proto.ModelConst.Type.LEN_TRANSPORT_ADD_2;
                this.icon3.high |= (y & proto.ModelConst.Type.LEN_TRANSPORT_ADD_3) << proto.ModelConst.Type.OFFSET_TRANSPORT_ADD_3;
            }
        }
        this.updateIconByFashion();
        // console.log("icon1:" + this.icon1.toNumber())
        // console.log("icon2:" + this.icon2.toNumber())
        // console.log("icon3:" + this.icon3.toNumber())
    }

    public getBagAllEquip() {
        return null == this.bag ? null : this.bag.getAllEquip();
    }

    public clonePlayer() {
        var e = new PlayerVo();
        e.setMyPlayer();
        e.id = this.id;
        e.level = this.level;
        e.hp = this.hp;
        e.mp = this.mp;
        e.hpMax = this.hpMax;
        e.mpMax = this.mpMax;
        e.cp = this.cp;
        e.str = this.str;
        e.con = this.con;
        e.agi = this.agi;
        e.ilt = this.ilt;
        e.wis = this.wis;
        e.itemSetData = this.itemSetData;
        e.titlePower1 = this.titlePower1;
        e.titlePowerValue1 = this.titlePowerValue1;
        e.titlePower2 = this.titlePower2;
        e.titlePowerValue2 = this.titlePowerValue2;
        e.power = this.power;
        e.powerValue = this.powerValue;
        e.powerExpireTime = this.powerExpireTime;
        e.fightPowerList = this.fightPowerList;
        e.skillList = this.skillList;
        e.bag = this.bag;
        e.level2 = this.level2;
        e.countrypowerValue = this.countrypowerValue;
        e.keepout_atk_time = this.keepout_atk_time;
        e.formationList = this.formationList;
        e.formationSkill = this.formationSkill;
        e.iconpet1 = this.iconpet1;
        this.playerTurnMonster && (e.playerTurnMonster = this.playerTurnMonster.clone());
        return e;
    }

    public getFreeSkillIndex(skillId: number) {
        if (null == this.skillList) return -1;
        if (0 >= skillId) return -1;
        var t = this.getSkillByID(skillId);
        return null != t ? Define.SUCCESS : Define.SUCCESS;
    }

    public learnSkill(skill: Skill) {
        if (null == skill) return -1;
        if (null == this.skillList) return -1;
        if (this.getFreeSkillIndex(skill.id) < 0) return -3;
        if (0 == this.skillList.length) {
            this.skillList.pushNoHas(skill)
            return Define.SUCCESS
        }
        for (let i = 0; i < this.skillList.length; i++) {
            const tmp = this.skillList[i]
            if (!tmp) continue
            if (tmp.id != skill.id) continue
            this.skillList.splice(i, 1, skill)
            return Define.SUCCESS
        }
        this.skillList.push(skill);
        return Define.SUCCESS;
    }

    public fromSkillData(e) {
        if (null != e) {
            for (var t = e.getByte(), i = 0; t > i; i++) {
                var o = e.getByte();
                if (1 == o) {
                    var n = Skill.fromBytes(e);
                    n.addLevel = e.getByte();
                    this.learnSkill(n);
                } else if (2 == o) {
                    var a = Skill.fromBytes(e);
                    a.addLevel = e.getByte();
                    this.learnSkill(a);
                } else if (3 == o) {
                    var r = e.getInt();
                    this.removeSkill(this.getSkillByID(r));
                }
            }
            var s = e.getByte();
            this.autoSkillID = [];
            for (var i = 0; s > i; i++) this.autoSkillID[i] = e.getShort();
            this.autoSkillID_Initiative = e.getShort();
        }
    }

    public fromAutoEquipSkillData(e) {
        if (null != e) {
            var t = e.getByte();
            this.autoSkillID = [];
            for (var i = 0; t > i; i++) this.autoSkillID[i] = e.getShort();
            this.autoSkillID_Initiative = e.getShort();
        }
    }

    public removeSkill(e) {
        if (null != e && !(null == this.skillList || this.skillList.length <= 0)) for (var t = 0; t < this.skillList.length; t++) {
            var i = this.skillList[t];
            if (null != i && i.id == e.id && i.level == e.level) {
                this.isAutoSkill(i.id) >= 0 && this.setAutoSkillInvalid(i.id)
                return void this.skillList.splice(t, 1)
            }
        }
    }

    // 宠物潜能替换后的操作  主要是移除旧的潜能技能
    public removeSkillSure(skill: Skill) {
        if (!skill) return
        for (let i = 0; i < this.skillList.length; i++) {
            const it = this.skillList[i]
            if (!it || it.id != skill.id) continue
            if (this.isAutoSkill(it.id) >= 0) {
                this.setAutoSkillInvalid(it.id)
            }
            this.skillList.splice(i, 1)
            break
        }
    }

    public getSkillByID(id: number) {
        if (null == this.skillList) return null;
        for (let i = 0; i < this.skillList.length; i++) {
            const skill = this.skillList[i];
            if (null != skill && skill.id == id) return skill;
        }
        return null;
    }

    public getSkillIndexByID(e) {
        if (null == this.skillList) return -1;
        for (var t = 0; t < this.skillList.length; t++) {
            var i = this.skillList[t];
            if (null != i && i.id == e) return t;
        }
        return -1;
    }

    public getSkillLevelByID(id: number) {
        const skill = this.getSkillByID(id);
        if (null == skill) return 0;
        let add = skill.addLevel ? skill.addLevel : 0;
        return skill.level - add;
    }

    public static getSkillListByType(e, t) {
        if (null == e || e.length <= 0) return null;
        for (var i = [], o = 0; o < e.length; o++) {
            var n = e[o];
            null != n && n.type == t && i.push(n);
        }
        return i;
    }

    // 设置自动释放技能
    public setAutoSkillActive(skillId: number) {
        if (null == this.autoSkillID) return -2
        for (let i = 0; i < this.autoSkillID.length; i++) {
            const id = this.autoSkillID[i];
            if (0 == id) {
                this.autoSkillID[i] = skillId
                return i
            }
        }
        return -1
    }

    // 取消设置自动释放技能
    public setAutoSkillInvalid(skillId: number) {
        if (null == this.autoSkillID) return -2
        let match = false
        let index = -1
        for (let i = 0; i < this.autoSkillID.length; i++) {
            const id = this.autoSkillID[i]
            if (id == skillId) {
                match = true
                index = i
                this.autoSkillID[i] = 0
            }
            if (match) {
                if (i < this.autoSkillID.length - 1) {
                    this.autoSkillID[i] = this.autoSkillID[i + 1]
                } else {
                    this.autoSkillID[i] = 0
                }
            }
        }
        return index;
    }

    /**
     * 判断是不是设置成为了自动技能
     * @param skillId
     * @returns 不等于-1则是设置了
     */
    public isAutoSkill(skillId: number) {
        for (let i = 0; i < this.autoSkillID.length; i++) {
            const id = this.autoSkillID[i]
            if (id == skillId) return i
        }
        return -1
    }

    public getAutoSkillCount() {
        for (var e = 0, t = 0; t < this.autoSkillID.length; t++) this.autoSkillID[t] > 0 && e++;
        return e;
    }

    public getMissionById(id: number) {
        const list = this.missionList
        if (!list) return null
        for (let i = 0; i < list.length; i++) {
            const mission = list[i]
            if (null != mission && mission.getId() == id) {
                return mission
            }
        }
        return null
    }

    public hasMission() {
        const list = this.missionList
        if (!list) return false
        for (let i = 0; i < list.length; i++) {
            const mission = list[i]
            if (mission) {
                return true
            }
        }
        return false
    }

    public getCurMapMission(e = true) {
        return MissionModel.mainMission ? MissionModel.mainMission : this.getLocalMapMission(e) || GameWorld.getCurMapCanAcceptMission();
    }

    public getLocalMapMission(e = true, t = true) {
        var i = this.missionList;
        if (i) {
            for (var n = [], a = [], r = 0; r < i.length; r++) {
                var s = i[r];
                if (s) {
                    var l = s.isCityOrCountry() || s.isEscort() || s.isEscortTeam();
                    if (!e) {
                        if (DAILY_MISSION_ID.includes(s.id) && s.status == MissionConst.NOT_CAN_SUBMIT) continue;
                        l ? a.push(s) : n.push(s);
                    }
                    (s.mapId == GameWorld.orgMapID || s.acceptMapId == GameWorld.orgMapID) && (l ? a.push(s) : n.push(s));
                }
            }
            t || (n = n.concat(a));
            return n.length > 0 ? n[0] : null;
        }
    }

    public getCanSubmitMission() {
        var e = this.missionList;
        if (e) {
            for (var t = 0; t < e.length; t++) {
                var i = e[t];
                if (null != i && i.isComplete(this)) return i;
            }
            return null;
        }
    }

    public addMission(missionObj: Mission, notSetAsCurMission = false) {
        if (null == missionObj) return false
        if (missionObj.isEscort()) return true
        let list = this.missionList
        if (!list) {
            list = this.missionList = new Array(Define.MAX_MISSION_SIZE)
        }
        if (null != this.getMissionById(missionObj.getId())) {
            return false
        }
        for (let i = 0; i < list.length; i++) {
            if (null == list[i]) {
                list[i] = missionObj
                MissionDef.setNewRadar(missionObj)
                if (!notSetAsCurMission) {
                    MainPlayerAi.ins.curMissionId = missionObj.id
                }
                return true
            }
        }
        return false
    }

    public deleteMission(missionObj: Mission) {
        return null == missionObj ? false : this.deleteMissionById(missionObj.getId());
    }

    public deleteMissionById(missionId: number) {
        const list = this.missionList
        if (null == list) return false
        for (let i = 0; i < list.length; i++) {
            const obj = list[i]
            if (null != obj && obj.getId() == missionId) {
                obj.cleanKillMission()
                MissionDef.clearNewRadar(obj)
                list[i] = null
                this.onDelMission(obj)
                return true
            }
        }
        return false
    }

    public onDelMission(missionObj: Mission) {
        const is = Mission.isMissionFinish(this, missionObj.id)
        if (is) {
            MainPlayerAi.ins.clearMissionById(missionObj.id)
            return
        }
        facade.sendNt(MissionEvent.ON_MAIN_MISSION_UPDATE)
    }

    public submitMission(missionObj: Mission) {
        return null == missionObj ? false : this.deleteMission(missionObj)
    }

    public setBag(bag: PlayerBag) {
        this.bag = bag
    }

    public getItemNumByIdInBag(e) {
        return null == this.bag ? 0 : this.bag.getItemNumByID(e);
    }

    public isEquipItemByIdInEquip(e) {
        return null == this.bag ? false : this.bag.isEquipItemByIdInEquip(e);
    }

    public static getCompareSprite(e, t) {
        console.log("引用错误放到ModelTool");
        return null;
    }

    /**
     * 恢复HP和MP
     */
    public resumeHPMP() {
        this.hp = this.get(proto.ModelConst.Type.HPMAX);
        this.mp = this.get(proto.ModelConst.Type.MPMAX);
    }

    /**
     * 检查最大生命值和法力值
     */
    public checkHPMP() {
        let hpMax = this.get(proto.ModelConst.Type.HPMAX);
        if (this.hp > hpMax) {
            this.hp = hpMax
        }
        let mpMax = this.get(proto.ModelConst.Type.MPMAX);
        if (this.mp > mpMax) {
            this.mp = mpMax
        }
    }

    public getMissionHaveItemDesc(e, t) {
        if (null != t) for (var i = null, o = 0; o < this.missionList.length; o++) {
            i = this.missionList[o];
            null != i && i.updateHaveItemDesc(e, t);
        }
    }

    public isHaveCanNotReliveBuffer() {
        var e = this.battleBufferList;
        if (null == e) return false;
        for (var t = e.arr, i = 0, o = t; i < o.length; i++) {
            var n = o[i];
            if (n.isCannotReliveStatus()) return true;
        }
        return false;
    }

    public setBattleStatus(e) {
        this.bStatus |= e;
        return true;
    }

    public clearBattleStatus(e) {
        this.bStatus &= ~e;
    }

    public isBattleStatus(e) {
        return 0 != (this.bStatus & e);
    }

    public isDead() {
        return this.hp <= 0;
    }

    /**
     * 是不是有 死亡傀儡状态
     * @returns 
     */
    public isDeadDelay() {
        if (!this.isBattleStatus(Define.getBufferBitValue(Define.BUFFER_RESIST_DIE_DELAY))) return false
        return !this.isTabStatus(proto.ModelConst.Type.BUFFER_DIE_DELAY_CHECK)
    }

    public isDeadNoWithDelay() {
        return this.isDead() && !this.isDeadDelay();
    }

    public addBuffer(e) {
        if (null != e) {
            null == this.battleBufferList && (this.battleBufferList = new Vector())
            Tool.debug("回合:" + e.battle.round + ",pos:" + this.position + ",addBuffer lastTime:" + e.lastTime + ",power:" + e.statusBit + ",attrID:" + e.attrID + ",addValue:" + e.addValue)
            if (e.isClearStatusBitBuffer()) this.battleBufferList.insertElementAt(e, 0); else {
                var t = this.setBattleStatus(Define.getBufferBitValue(e.getStatus()));
                !t ? e.clearStatus() : this.checkBufferSize(e);
                this.battleBufferList.addElement(e);
                t && this.setBattleStatus(Define.getBufferBitValue(e.getStatus()));
            }
        }
    }

    public getbuffer(e) {
        var t = this.battleBufferList;
        if (t.size() <= 0) return null;
        for (var i = t.size() - 1; i >= 0; i--) {
            var o = t.elementAt(i);
            if (null != o && o.getStatus() != Define.BUFFER_NONE && o.getStatus() == e) return o;
        }
        return null;
    }

    public checkBufferSize(e) {
        if (e.getStatus() != Define.BUFFER_NONE) {
            for (var t = 0, i = Define.BUFFER_NONE, o = this.battleBufferList.size() - 1; o >= 0; o--) {
                var a = this.battleBufferList.elementAt(o);
                if (null != a && a.getStatus() != Define.BUFFER_NONE) if (e.isDieStatus() && a.isDieStatus()) {
                    this.clearBattleStatus(Define.getBufferBitValue(a.getStatus()));
                    a.destroy(this);
                    this.battleBufferList.removeElement(a);
                } else if (Define.getBufferType(a.getStatus()) == Define.getBufferType(e.getStatus()) && (t++, t >= BattleDefine.BUFF_MAX_SIZE)) {
                    i = a.getStatus();
                    a.destroy(this);
                    this.battleBufferList.removeElement(a);
                    break;
                }
            }
            if (i != Define.BUFFER_NONE) {
                for (var o = 0; o < this.battleBufferList.size(); o++) {
                    var a = this.battleBufferList.elementAt(o);
                    if (null != a && a.getStatus() == i) return;
                }
                this.clearBattleStatus(Define.getBufferBitValue(i));
            }
        }
    }

    public clearBufferList(e) {
        if (null != this.battleBufferList) {
            for (var t = this.battleBufferList.size() - 1; t >= 0; t--) {
                var i = this.battleBufferList.elementAt(t);
                null != i && (0 == e && i.isCannotReliveStatus() || (i.destroy(this), this.battleBufferList.removeElement(i), this.clearBattleStatus(Define.getBufferBitValue(i.getStatus()))));
            }
            this.battleBufferList.size() <= 0 && (this.battleBufferList = null);
        }
    }

    public removeBufferByStatus(e) {
        if (e = 255 & e, null != this.battleBufferList) for (var t = this.battleBufferList.size() - 1; t >= 0; t--) {
            var i = this.battleBufferList.elementAt(t);
            if (null != i) {
                var o = i.isSameStatusType(e);
                o && (i.destroy(this), this.battleBufferList.removeElement(i), this.clearBattleStatus(Define.getBufferBitValue(i.getStatus())));
            }
        }
    }

    public runBufferList(e: boolean) {
        if (null == this.battleBufferList) return null
        const controls = new Vector<Control>()
        const removeBuffers = new Vector<Buffer>()

        for (let i = 0; i < this.battleBufferList.size(); i++) {
            const buffer = this.battleBufferList.elementAt(i);
            if (!buffer) continue
            if (!e) {
                buffer.getLastTime() <= 0 && removeBuffers.addElement(buffer)
                continue
            }
            buffer.run(this, controls)
            if (null == this.battleBufferList) return controls
            if (!buffer.isClearStatusBitBuffer()) continue

            for (let j = i + 1; j < this.battleBufferList.size(); j++) {
                const tmpBuffer = this.battleBufferList.elementAt(j);
                if (!tmpBuffer) continue
                if (!tmpBuffer.isSameStatusType(255 & buffer.getAddValue())) continue
                tmpBuffer.finish()
                removeBuffers.addElement(tmpBuffer)
            }
        }
        // 没有移除任何buff
        if (removeBuffers.size() <= 0) return controls
        for (let i = 0; removeBuffers.size() > i; i++) {
            const buffer = removeBuffers.elementAt(i)
            buffer.destroy(this)
            this.battleBufferList.removeElement(buffer)
            this.clearBattleStatus(Define.getBufferBitValue(buffer.getStatus()))
        }
        for (let i = 0; i < this.battleBufferList.size(); i++) {
            const buffer = this.battleBufferList.elementAt(i)
            if (!buffer) continue
            this.setBattleStatus(Define.getBufferBitValue(buffer.getStatus()))
        }
        return controls
    }

    public getAttackAnimePos() {
        var e = this.getEquipWeaponType();
        return e == Define.ITEM_TYPE_WEAPON_ONEHAND_CROSSBOW || e == Define.ITEM_TYPE_WEAPON_TWOHAND_CROSSBOW || e == Define.ITEM_TYPE_WEAPON_TWOHAND_BOW || e == Define.ITEM_TYPE_WEAPON_TWOHAND_STAFF || e == Define.ITEM_TYPE_WEAPON_BALL || e == Define.ITEM_TYPE_WEAPON_ONEHAND_GUN || e == Define.ITEM_TYPE_WEAPON_TWOHAND_GUN || e == Define.ITEM_TYPE_WEAPON_TWOHAND_FAN ? Define.SKILL_POS_STAND : Define.SKILL_POS_FRONT;
    }

    public doKeepAtkTime() {
        if (this.addValue(proto.ModelConst.Type.KEEPOUT_ATK_TIME, -1), null != this.battleBufferList) for (var e = this.battleBufferList.size() - 1; e >= 0; e--) {
            var t = this.battleBufferList.elementAt(e);
            if (null != t && t.useKeeAtkTime()) return;
        }
    }

    public getAttackRangeAnime() {
        var e = this.getEquipWeaponType();
        return e == Define.ITEM_TYPE_WEAPON_ONEHAND_CROSSBOW || e == Define.ITEM_TYPE_WEAPON_TWOHAND_CROSSBOW ? SprConst.SPR_INDEX_EFF_START + SprConst.EFF_RANGE : 0;
    }

    public refreshWorldMercenaryData() {
        null != this.worldMer && (this.worldMer.hp = this.hp, this.worldMer.mp = this.mp);
    }

    public resetBattleBuffer() {
        this.bStatus = 0;
        this.battleBufferList && (this.battleBufferList.length = 0, this.battleBufferList.arr = null, this.battleBufferList = null);
        this.argo = 0;
        this.hpMax = 0;
        this.mpMax = 0;
        this.speed = 0;
        this.atk_str = 0;
        this.atk_agi = 0;
        this.atk_magic = 0;
        this.atk_time = 0;
        this.def_str = 0;
        this.def_agi = 0;
        this.def_magic = 0;
        this.dodge = 0;
        this.hitrate = 0;
        this.hitMagic = 0;
        this.critical = 0;
        this.forceHit = 0;
        this.wil = 0;
        this.tough = 0;
        this.block = 0;
        this.brkArmor = 0;
        this.insight = 0;
        this.def_field = 0;
        this.back = 0;
        this.magic_back = 0;
        this.life_absorption = 0;
        this.mana_absorption = 0;
        this.magic_penetration = 0;
        this.heal_recovery = 0;
        this.mana_recovery = 0;
        this.recovery = 0;
        this.keepout_atk_time = 0;
        this.setTabStatus(false, proto.ModelConst.Type.BUFFER_DIE_1HP_CHECK);
        this.setTabStatus(false, proto.ModelConst.Type.BUFFER_DIE_FULLHP_CHECK);
        this.setTabStatus(false, proto.ModelConst.Type.BUFFER_DIE_DELAY_CHECK);
    }

    public updateAllKillMission(monsterIdAry: number[], cntAry: number[], buffer: StringBuffer) {
        if (null != monsterIdAry && null != cntAry && monsterIdAry.length == cntAry.length) {
            for (let i = 0; i < monsterIdAry.length; i++) {
                let monsterId = monsterIdAry[i];
                for (let j = 0; j < this.missionList.length; j++) {
                    const mis = this.missionList[j];
                    if (null != mis) {
                        mis.updateKillMission(monsterId, cntAry[i], buffer)
                    }
                }
            }
        }
    }

    public updateKillMission(battleGroupId: number, buffer: StringBuffer) {
        const group = CfgHelper.getBattleGroup(battleGroupId)
        if (!group || !group.monsters) return
        const ary = group.monsters
        const mapObj = {}
        for (let i = 0; i < ary.length; i++) {
            const key = ary[i]
            if (!key) continue
            mapObj[key] = mapObj[key] ? mapObj[key] + 1 : 1
        }
        for (let id in mapObj) {
            const monsterId = Number(id)
            for (let j = 0; j < this.missionList.length; j++) {
                const mis = this.missionList[j];
                if (null != mis) {
                    mis.updateKillMission(monsterId, mapObj[id], buffer)
                }
            }
        }
        facade.sendNt(SceneEvent.ON_NPC_SIGN_UPDATE)
    }

    public fromBytesByAutoMove(e) {
        for (var t = [], i = e.getByte(), o = 0; i > o; o++) {
            var n = e.getByte(),
                a = e.getByte(),
                r = e.getShort(),
                s = e.getByte(),
                l = e.getByte();
            t.push([n, a]);
            var u = [r, s, l];
            t.push(u);
        }
        var _ = e.getByte(),
            d = e.getByte();
        t.push([_, d]);
        MainPlayerAi.ins.startAutoMove(t);
    }

    public setGridXY(e, t) {
        this.gridX = e;
        this.gridY = t;
    }

    public getGridX() {
        return this.gridX;
    }

    public getGridY() {
        return this.gridY;
    }

    public isAutoMove() {
        return this._isAutoMoving;
    }

    public getGenreIDForWeaponType() {
        var e = null;
        null != this.bag && (e = this.bag.getItem(PlayerBag.WEAPON_LEFT_POS));
        null != this.bag && null == e && (e = this.bag.getItem(PlayerBag.WEAPON_RIGHT_POS));
        var t = 0;
        if (null == e) return t = 1;
        switch (e.type) {
            case Define.ITEM_TYPE_WEAPON_ONEHAND_BLADE:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_BLADE:
                t = 1;
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_SWORD:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_SWORD:
                t = 2;
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_LANCE:
                t = 3;
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_CROSSBOW:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_CROSSBOW:
                t = 4;
                break;
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HEAVY:
            case Define.ITEM_TYPE_WEAPON_TWOHAND_HEAVY:
                t = 5;
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_BOW:
                t = 6;
                break;
            case Define.ITEM_TYPE_WEAPON_TWOHAND_STAFF:
            case Define.ITEM_TYPE_WEAPON_ONEHAND_HAND:
                t = 7;
        }
        return t;
    }

    /**
     * 增加经验值 并且会检查能不能升级
     * @param exp 经验值
     * @param buffer 信息
     */
    public addAndCheckExpForUpgrade(exp: number, buffer: StringBuffer) {
        let addLevel1 = 0
        let addExp = exp
        let addLevel2 = 0
        let addExp2 = 0
        let addSP = 0;
        let addCP = 0;

        const CurrentExp = () => {
            return this.level >= CfgHelper.MAX_LEVEL ? this.exp2 : this.exp
        }
        const MaxExp = () => {
            let isCq = this.level >= CfgHelper.MAX_LEVEL
            const max = CfgHelper.getLevelUpExp(isCq ? this.level2 : this.level)
            if (isCq) {
                this.expMax2 = max * 2
                return this.expMax2
            }
            this.expMax = max
            return this.expMax
        }
        const DeductExp = (v: number) => {
            if (this.level >= CfgHelper.MAX_LEVEL) {
                this.exp2 -= v
                return
            }
            this.exp -= v
        }

        this.addValue(this.level >= CfgHelper.MAX_LEVEL ? proto.ModelConst.Type.EXP2 : proto.ModelConst.Type.EXP, exp)

        const AddLevel = (v: number) => {
            let isCq = this.level >= CfgHelper.MAX_LEVEL
            if (isCq) {
                addLevel2 += v
                this.addValue(proto.ModelConst.Type.LEVEL2, v)
                return
            }

            this.addValue(proto.ModelConst.Type.LEVEL, v)
            // 如果满级 则传奇到1级  更新传奇经验 同时清理普通经验数据
            if (this.level >= CfgHelper.MAX_LEVEL) {
                this.level2 = 1
                this.expMax = 0
                this.exp2 = this.exp
                this.exp = 0
                addExp2 = this.exp2
                addExp -= addExp2
            }

            addLevel1 += v
            addCP += CfgHelper.misc.role.attributePoint
            addSP += CfgHelper.misc.role.skillPoint * this.level
        }

        while (CurrentExp() >= MaxExp()) {
            DeductExp(MaxExp())
            AddLevel(1)
        }

        if (addSP > 0) {
            this.addValue(proto.ModelConst.Type.SP, addSP)
        }
        if (addCP > 0) {
            this.addValue(proto.ModelConst.Type.CP, addCP)
            this.setTabStatus(true, proto.ModelConst.Type.ATTR_NEW_NOTICE)
        }

        if (buffer) {
            if (addExp > 0) {
                buffer.append(Tool.manageString(GameText.STR_UPDATE_LV_EXP, addExp + ""))
            }
            if (addLevel1 > 0) {
                buffer.append(Tool.manageString(GameText.STR_CONG_UPDATE_LV, addLevel1 + ""))
            }
            if (addCP > 0) {
                buffer.append(PowerString.makeColorString(GameText.STR_CP_POINT + "+" + addCP, Tool.COLOR_YELLOW) + "\n")
            }
            if (addSP > 0) {
                buffer.append(PowerString.makeColorString(GameText.STR_SP_POINT + "+" + addSP, Tool.COLOR_YELLOW) + "\n")
            }
            if (addExp2 > 0) {
                buffer.append(Tool.manageString(GameText.STR_UPDATE_LV_EXP2, addExp2 + ""))
            }
            if (addLevel2 > 0) {
                buffer.append(Tool.manageString(GameText.STR_CONG_UPDATE_LV2, addLevel2 + ""))
            }
        }

        if (addLevel1 > 0 || addLevel2 > 0) {
            facade.showView(ModName.Main, MainViewType.LevelUpEffect, {
                gainLevel: addLevel1,
                gainlevel2: addLevel2
            })
            facade.sendNt(MainEvent.ON_LEVEL_UP)
            this.resumeHPMP()
        }
    }
}

import Mission, { MissionConst } from "../mission/data/Mission";
import { DAILY_MISSION_ID, MainPlayerAi } from "../MainPlayerAi";
import { BookGroupModel } from "../raiders/data/BookGroupModel";
import Item from "./Item";
import Skill from "../role/data/Skill";
import { GUtil } from "../gameCore/GUtil";
import PlayerBag from "../bag/data/PlayerBag";
import { Define, MODEL_TYPE } from "../gameCore/Define";
import { SprConst } from "../gameCore/gs/SprConst";
import CfgHelper from "../helper/CfgHelper"; import Condition from "../mission/data/Condition";
import { SceneEvent } from "../scene/define/SceneEvent";
import Control from "../battle/data/Control";

