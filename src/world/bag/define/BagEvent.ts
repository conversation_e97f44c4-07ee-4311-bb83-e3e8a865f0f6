export enum BagEvent {
    BAG_EQUIPMENT_UPDATE = "bag_equipment_update",
    BAG_ITEM_UPDATE = "bag_item_update",
    BAG_CHANGE = "bag_change",
    BAG_INFO = "bag_info",
    BAG_ITEM_DELETE = "bag_item_delete",
    BAG_ITEM_INPUT = "bag_item_input",
    BAG_ITEM_INPUT_CANCEL = "bag_item_input_cancel",
    BAG_ITEM_DEL = "bag_item_del",
    BAG_ITEM_STARINFO = "bag_item_starInfo",
    BAG_ITEM_ATTACH = "bag_item_attach",
    BAG_ITEM_ATTACHCHANGE = "bag_item_attachChange",
    BAG_SELFSHOP_UP = "bag_selfShop_up",
    BAG_SELFSHOP_DOWN = "bag_selfShop_down",
    BAG_STORAGE_UPDATE = "bag_storage_update",
    BAG_STORAGE_REFRESH = "bag_storage_Refresh",
    BAG_PLAYER_ENCHANT = "bag_player_enchant",
    BAG_CHANGE_DURABILITY = "bag_change_Durability",
    BAG_MONSTER_CHANGED = "bag_monster_changed",
    BAG_ITEM_STARATTR = "bag_item_starAttr",
    BAG_ITEM_BETTER = "bag_item_better",
    BAG_ITEM_FORGUID = "bag_item_forGuid",
    BAG_FIX = "bag_fix",
    BAG_FULL = "bag_full",
    BAG_CHECK_FULL = "bag_check_full",
    DO_ERROR_JUMP_SHOP_OK = "DO_ERROR_JUMP_SHOP_OK",
    BAG_PET_EQUIP_SELECT = "BAG_PET_EQUIP_SELECT",
    BAG_PET_EQUIP_INFO = "BAG_PET_EQUIP_INFO",
    BAG_PET_EQUIP_PRO_ANIM = "BAG_PET_EQUIP_PRO_ANIM"
}
