import BagItemPutView from "../view/BagItemPutView";
import MdrBase from "../../gameCore/MdrBase";
import {Layer} from "../../Layer";
import Item from "../../vo/Item";
import {BagEvent} from "../define/BagEvent";
import TextUtil from "../../gameCore/TextUtil";
import {Define} from "../../gameCore/Define";
import GameWorld from "../../GameWorld";
import UIHandler from "../../UIHandler";
import {ModName} from "../../viewType/ModName";
import {BagViewType} from "../../viewType/BagViewType";

export class BagItemInputMdr extends MdrBase<BagItemPutView, any> {

    public item: Item

    constructor() {
        super(Layer.modal)
        this.mark("_view", BagItemPutView);
        this.isEasyHide = true;
    }

    public onInit() {
        this._view.horizontalCenter = 0;
        this._view.verticalCenter = 0;
        this._view.textNum.visible = false;
    }

    public addListeners() {
        this.listen(this._view.btnClose, this.tapClose);
        this.listen(this._view.btnCancel, this.tapClose);
        this.listen(this._view.btnSure, this.onSure);
        this.listen(this._view.btnReduce, this.onReduce);
        this.listen(this._view.btnAdd, this.onAdd);
        this.listen(this._view.sliderNum, this.onHSliderChange, egret.TouchEvent.CHANGE);
    }

    public onHSliderChange() {
        this._view.textCurVal.text = "" + this._view.sliderNum.value;
    }

    public onReduce() {
        this.onChange(-1);
    }

    public onAdd() {
        this.onChange(1);
    }

    public onChange(e) {
        var t = this._view.sliderNum.value + e;
        t < this._view.sliderNum.minimum || t > this._view.sliderNum.maximum || (this._view.sliderNum.value = t, this.onHSliderChange());
    }

    public onSure() {
        this.item.quantity = this._view.sliderNum.value;
        this.sendNt(BagEvent.BAG_ITEM_INPUT, this.item);
        this.tapClose();
    }

    public initView() {
        var e = this._showArgs.inBag,
            t = this._showArgs.item;
        this.item = t;
        var i = t.quantity,
            o = 1;
        this._view.sliderNum.maximum = i;
        this._view.sliderNum.minimum = o;
        this._view.txtMax.text = "" + i;
        this._view.txtMin.text = "" + o;
        this._view.textCurVal.text = "" + i;
        this._view.sliderNum.value = i;
        var n = i > 1;
        this._view.groupNum.visible = this._view.groupNum.includeInLayout = n;
        var a = t.getNameInfo(),
            s = TextUtil.parseHtml(a);
        this._view.textItemName.textFlow = s;
        this._view.textItemBingMsg.text = t.getBingMsg();
        this._view.textItemType.text = Define.getItemTypeString(t.type);
        this._view.comItem.data = t;
        var _ = t.getDesc(GameWorld.myPlayer, this._view.textDes.width),
            d = TextUtil.parseHtml(_);
        if (this._view.textDes.textFlow = d, e) {
            var p = t.getItemClass(),
                h = t.isEquited();
            switch (p) {
                case Item.ITEM_CLASS_WEAPON:
                case Item.ITEM_CLASS_ARMOR:
                    h ? this._view.currentState = "equipped" : this._view.currentState = "equipment";
                    break;
                case Item.ITEM_CLASS_PET:
                    h ? this._view.currentState = "petEquiped" : this._view.currentState = "pet";
                    break;
                case Item.ITEM_CLASS_USE_ITEM:
                    this._view.currentState = "item";
                    break;
                default:
                    this._view.currentState = "other";
            }
        } else this._view.currentState = "other";
    }

    public onShow() {
        this.initView();
    }

    public tapClose() {
        this.sendNt(BagEvent.BAG_ITEM_INPUT_CANCEL, this.item);
        UIHandler.hideView(ModName.Bag, BagViewType.BagItemInput);
    }

}

